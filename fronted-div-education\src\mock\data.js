// Mock数据 - DIV教育学习平台

// 用户数据
export const users = [
  {
    id: 1,
    username: "张三",
    email: "<PERSON><PERSON><PERSON>@example.com",
    avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    role: "student",
    joinDate: "2024-01-15",
    learningHours: 120,
    completedCourses: 15,
    points: 2580,
    level: "高级学员"
  },
  {
    id: 2,
    username: "李老师",
    email: "<EMAIL>",
    avatar: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
    role: "teacher",
    joinDate: "2023-08-10",
    learningHours: 0,
    completedCourses: 0,
    points: 5200,
    level: "资深讲师"
  },
  {
    id: 3,
    username: "王小明",
    email: "<EMAIL>",
    avatar: "https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png",
    role: "student",
    joinDate: "2024-03-20",
    learningHours: 85,
    completedCourses: 8,
    points: 1650,
    level: "中级学员"
  }
];

// 课程分类数据
export const categories = [
  { id: 1, name: "前端开发", description: "HTML、CSS、JavaScript、Vue、React等", parentId: null, sortOrder: 1 },
  { id: 2, name: "后端开发", description: "Java、Python、Node.js、数据库等", parentId: null, sortOrder: 2 },
  { id: 3, name: "移动开发", description: "Android、iOS、Flutter、React Native等", parentId: null, sortOrder: 3 },
  { id: 4, name: "数据科学", description: "机器学习、数据分析、人工智能等", parentId: null, sortOrder: 4 },
  { id: 5, name: "设计相关", description: "UI设计、UX设计、平面设计等", parentId: null, sortOrder: 5 }
];

// 课程数据
export const courses = [
  {
    id: 1,
    title: "Vue.js从入门到精通",
    description: "全面学习Vue.js框架，包含基础语法、组件开发、状态管理等核心知识点，适合零基础学员",
    coverImage: "https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png",
    categoryId: 1,
    categoryName: "前端开发",
    instructor: {
      id: 2,
      name: "李老师",
      avatar: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
    },
    duration: 1800,
    difficulty: "intermediate",
    rating: 4.8,
    studentsCount: 1250,
    price: 0,
    tags: ["Vue", "JavaScript", "前端"],
    chapters: [
      { id: 1, title: "Vue基础语法", duration: 300, videoUrl: "https://example.com/video/1.mp4" },
      { id: 2, title: "组件开发", duration: 450, videoUrl: "https://example.com/video/2.mp4" },
      { id: 3, title: "路由管理", duration: 360, videoUrl: "https://example.com/video/3.mp4" }
    ],
    createdAt: "2024-01-10"
  },
  {
    id: 2,
    title: "Java Spring Boot实战",
    description: "深入学习Spring Boot框架，掌握企业级Java开发技能，包含RESTful API、数据库操作等",
    coverImage: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    categoryId: 2,
    categoryName: "后端开发",
    instructor: {
      id: 2,
      name: "李老师",
      avatar: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
    },
    duration: 2400,
    difficulty: "advanced",
    rating: 4.9,
    studentsCount: 890,
    price: 0,
    tags: ["Java", "Spring Boot", "后端"],
    chapters: [
      { id: 4, title: "Spring Boot入门", duration: 400, videoUrl: "https://example.com/video/4.mp4" },
      { id: 5, title: "数据库集成", duration: 500, videoUrl: "https://example.com/video/5.mp4" }
    ],
    createdAt: "2024-02-15"
  },
  {
    id: 3,
    title: "Python数据分析基础",
    description: "使用Python进行数据分析，学习pandas、numpy、matplotlib等核心库的使用",
    coverImage: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
    categoryId: 4,
    categoryName: "数据科学",
    instructor: {
      id: 2,
      name: "李老师",
      avatar: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
    },
    duration: 1500,
    difficulty: "beginner",
    rating: 4.6,
    studentsCount: 2100,
    price: 0,
    tags: ["Python", "数据分析", "机器学习"],
    chapters: [
      { id: 6, title: "Python基础", duration: 300, videoUrl: "https://example.com/video/6.mp4" },
      { id: 7, title: "数据处理", duration: 400, videoUrl: "https://example.com/video/7.mp4" }
    ],
    createdAt: "2024-03-01"
  }
];

// 社区帖子数据
export const posts = [
  {
    id: 1,
    title: "Vue3 Composition API 最佳实践分享",
    content: "在使用Vue3开发项目的过程中，我总结了一些Composition API的最佳实践，希望对大家有帮助...",
    author: {
      id: 1,
      username: "张三",
      avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
      level: "高级学员"
    },
    images: ["https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png"],
    tags: ["Vue3", "经验分享"],
    likesCount: 128,
    commentsCount: 45,
    viewsCount: 890,
    isLiked: false,
    createdAt: "2024-08-20T10:30:00Z"
  },
  {
    id: 2,
    title: "Spring Boot项目部署经验总结",
    content: "最近完成了一个Spring Boot项目的部署，遇到了一些问题，在这里分享一下解决方案...",
    author: {
      id: 3,
      username: "王小明",
      avatar: "https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png",
      level: "中级学员"
    },
    images: [],
    tags: ["Spring Boot", "部署"],
    likesCount: 89,
    commentsCount: 23,
    viewsCount: 456,
    isLiked: true,
    createdAt: "2024-08-19T15:20:00Z"
  }
];

// 评论数据
export const comments = [
  {
    id: 1,
    postId: 1,
    user: {
      id: 3,
      username: "王小明",
      avatar: "https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png"
    },
    content: "很有用的分享，学到了很多！",
    parentId: null,
    createdAt: "2024-08-20T11:00:00Z"
  },
  {
    id: 2,
    postId: 1,
    user: {
      id: 2,
      username: "李老师",
      avatar: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
    },
    content: "总结得很全面，推荐给其他同学",
    parentId: null,
    createdAt: "2024-08-20T12:15:00Z"
  }
];

// 学习小组数据
export const studyGroups = [
  {
    id: 1,
    name: "前端学习小组",
    description: "一起学习前端技术，分享经验，共同进步",
    coverImage: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    creator: {
      id: 1,
      username: "张三",
      avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
    },
    memberCount: 156,
    maxMembers: 500,
    isPublic: true,
    tags: ["前端", "JavaScript", "Vue"],
    createdAt: "2024-07-15",
    recentActivity: "2小时前有新讨论"
  },
  {
    id: 2,
    name: "Java开发交流群",
    description: "Java后端开发技术交流，欢迎各级别开发者加入",
    coverImage: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
    creator: {
      id: 2,
      username: "李老师",
      avatar: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
    },
    memberCount: 89,
    maxMembers: 200,
    isPublic: true,
    tags: ["Java", "Spring", "后端"],
    createdAt: "2024-06-20",
    recentActivity: "1天前有新讨论"
  }
];

// 简讯数据
export const news = [
  {
    id: 1,
    title: "Vue 3.4 正式发布，带来重大性能提升",
    summary: "Vue 3.4版本正式发布，在编译器优化、响应式系统等方面带来显著改进...",
    coverImage: "https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png",
    category: "技术资讯",
    readTime: 3,
    viewsCount: 2340,
    publishedAt: "2024-08-22T09:00:00Z",
    isHot: true
  },
  {
    id: 2,
    title: "2024年前端开发趋势预测",
    summary: "分析2024年前端开发的主要趋势，包括新框架、工具链发展等...",
    coverImage: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    category: "行业动态",
    readTime: 5,
    viewsCount: 1890,
    publishedAt: "2024-08-21T14:30:00Z",
    isHot: false
  }
];

// 学习记录数据
export const learningRecords = [
  {
    id: 1,
    userId: 1,
    courseId: 1,
    progress: 75,
    lastPosition: 1350,
    completedAt: null,
    createdAt: "2024-08-15T10:00:00Z",
    updatedAt: "2024-08-22T16:30:00Z"
  },
  {
    id: 2,
    userId: 1,
    courseId: 3,
    progress: 100,
    lastPosition: 1500,
    completedAt: "2024-08-20T20:15:00Z",
    createdAt: "2024-08-10T09:00:00Z",
    updatedAt: "2024-08-20T20:15:00Z"
  }
];

// 仿真实验数据
export const experiments = [
  {
    id: 1,
    title: "单摆运动实验",
    description: "通过改变摆长和质量，观察单摆的周期变化规律",
    subject: "物理",
    difficulty: "beginner",
    duration: 30, // 分钟
    coverImage: "https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png",
    tags: ["力学", "振动", "周期运动"],
    objectives: [
      "理解单摆运动的基本规律",
      "掌握周期与摆长的关系",
      "学会使用实验数据分析方法"
    ],
    equipment: ["单摆装置", "秒表", "米尺", "小球"],
    steps: [
      "设置摆长为1米，测量10个周期的时间",
      "改变摆长为0.5米，重复测量",
      "改变摆长为1.5米，重复测量",
      "记录数据并分析周期与摆长的关系"
    ],
    completedCount: 1250,
    rating: 4.7,
    createdAt: "2024-07-01"
  },
  {
    id: 2,
    title: "酸碱中和滴定实验",
    description: "使用标准NaOH溶液滴定未知浓度的HCl溶液",
    subject: "化学",
    difficulty: "intermediate",
    duration: 45,
    coverImage: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    tags: ["酸碱反应", "滴定", "定量分析"],
    objectives: [
      "掌握酸碱滴定的基本操作",
      "理解滴定终点的判断方法",
      "学会计算未知溶液的浓度"
    ],
    equipment: ["滴定管", "锥形瓶", "指示剂", "标准溶液"],
    steps: [
      "准备标准NaOH溶液和待测HCl溶液",
      "在锥形瓶中加入待测溶液和指示剂",
      "用标准溶液进行滴定至终点",
      "记录消耗的标准溶液体积并计算浓度"
    ],
    completedCount: 890,
    rating: 4.8,
    createdAt: "2024-07-15"
  },
  {
    id: 3,
    title: "光的折射实验",
    description: "研究光在不同介质中的折射现象，验证斯涅尔定律",
    subject: "物理",
    difficulty: "intermediate",
    duration: 35,
    coverImage: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
    tags: ["光学", "折射", "斯涅尔定律"],
    objectives: [
      "观察光的折射现象",
      "测量不同入射角对应的折射角",
      "验证斯涅尔定律的正确性"
    ],
    equipment: ["激光器", "半圆形玻璃砖", "量角器", "白纸"],
    steps: [
      "设置实验装置，调整激光器位置",
      "改变入射角，测量对应的折射角",
      "记录多组数据",
      "计算折射率并验证斯涅尔定律"
    ],
    completedCount: 650,
    rating: 4.5,
    createdAt: "2024-08-01"
  }
];

// 实验记录数据
export const experimentRecords = [
  {
    id: 1,
    userId: 1,
    experimentId: 1,
    status: "completed", // pending, in_progress, completed
    score: 85,
    timeSpent: 28, // 分钟
    attempts: 2,
    data: {
      measurements: [
        { length: 1.0, period: 2.01 },
        { length: 0.5, period: 1.42 },
        { length: 1.5, period: 2.45 }
      ],
      analysis: "实验数据符合T=2π√(L/g)的理论公式"
    },
    completedAt: "2024-08-20T14:30:00Z",
    createdAt: "2024-08-20T14:00:00Z"
  },
  {
    id: 2,
    userId: 1,
    experimentId: 2,
    status: "in_progress",
    score: null,
    timeSpent: 15,
    attempts: 1,
    data: {
      measurements: [
        { volume: 10.5, concentration: null }
      ]
    },
    completedAt: null,
    createdAt: "2024-08-22T10:00:00Z"
  }
];

// 学习行为分析数据
export const learningAnalytics = {
  userId: 1,
  totalStudyTime: 120, // 小时
  coursesCompleted: 15,
  experimentsCompleted: 8,
  postsPublished: 12,
  averageScore: 87.5,
  studyStreak: 15, // 连续学习天数
  weeklyActivity: [
    { day: "周一", hours: 2.5, courses: 1, experiments: 0 },
    { day: "周二", hours: 3.2, courses: 0, experiments: 1 },
    { day: "周三", hours: 1.8, courses: 1, experiments: 0 },
    { day: "周四", hours: 4.1, courses: 2, experiments: 1 },
    { day: "周五", hours: 2.9, courses: 1, experiments: 0 },
    { day: "周六", hours: 3.5, courses: 1, experiments: 2 },
    { day: "周日", hours: 2.0, courses: 0, experiments: 1 }
  ],
  monthlyProgress: [
    { month: "1月", studyHours: 25, completedCourses: 3, experiments: 2 },
    { month: "2月", studyHours: 32, completedCourses: 4, experiments: 3 },
    { month: "3月", studyHours: 28, completedCourses: 2, experiments: 1 },
    { month: "4月", studyHours: 35, completedCourses: 6, experiments: 2 }
  ],
  skillAssessment: {
    frontend: { level: 85, progress: 12 },
    backend: { level: 72, progress: 8 },
    database: { level: 68, progress: 15 },
    algorithm: { level: 79, progress: 6 },
    design: { level: 63, progress: 20 }
  },
  learningPath: [
    { skill: "HTML/CSS", completed: true, score: 92 },
    { skill: "JavaScript", completed: true, score: 88 },
    { skill: "Vue.js", completed: true, score: 85 },
    { skill: "Node.js", completed: false, progress: 60 },
    { skill: "数据库设计", completed: false, progress: 30 }
  ]
};
