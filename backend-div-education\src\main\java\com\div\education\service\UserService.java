package com.div.education.service;

import com.div.education.dto.PageResponse;
import com.div.education.dto.UserDTO;
import com.div.education.entity.User;
import com.div.education.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 用户服务类
 */
@Service
@RequiredArgsConstructor
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * 根据邮箱查找用户
     */
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * 根据用户名查找用户
     */
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 根据ID查找用户
     */
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * 创建用户
     */
    public User createUser(String username, String email, String password, String nickname, User.UserRole role) {
        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(email)) {
            throw new RuntimeException("邮箱已存在");
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(username)) {
            throw new RuntimeException("用户名已存在");
        }

        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(passwordEncoder.encode(password));
        user.setNickname(nickname != null ? nickname : username);
        user.setRole(role != null ? role : User.UserRole.STUDENT);
        user.setStatus(User.UserStatus.ACTIVE);

        return userRepository.save(user);
    }

    /**
     * 更新用户信息
     */
    public User updateUser(Long userId, String nickname, String avatar, String phone, String bio) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (nickname != null) user.setNickname(nickname);
        if (avatar != null) user.setAvatar(avatar);
        if (phone != null) user.setPhone(phone);
        if (bio != null) user.setBio(bio);

        return userRepository.save(user);
    }

    /**
     * 验证密码
     */
    public boolean validatePassword(User user, String password) {
        return passwordEncoder.matches(password, user.getPassword());
    }

    /**
     * 修改密码
     */
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    /**
     * 分页查询用户
     */
    @Transactional(readOnly = true)
    public PageResponse<UserDTO> getUsers(Pageable pageable) {
        Page<User> userPage = userRepository.findAll(pageable);
        Page<UserDTO> dtoPage = userPage.map(UserDTO::fromEntity);
        return PageResponse.of(dtoPage);
    }

    /**
     * 搜索用户
     */
    @Transactional(readOnly = true)
    public PageResponse<UserDTO> searchUsers(String keyword, Pageable pageable) {
        Page<User> userPage = userRepository.searchUsers(keyword, pageable);
        Page<UserDTO> dtoPage = userPage.map(UserDTO::fromEntity);
        return PageResponse.of(dtoPage);
    }

    /**
     * 根据角色查询用户
     */
    @Transactional(readOnly = true)
    public PageResponse<UserDTO> getUsersByRole(User.UserRole role, Pageable pageable) {
        Page<User> userPage = userRepository.findByRole(role, pageable);
        Page<UserDTO> dtoPage = userPage.map(UserDTO::fromEntity);
        return PageResponse.of(dtoPage);
    }

    /**
     * 删除用户
     */
    public void deleteUser(Long userId) {
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("用户不存在");
        }
        userRepository.deleteById(userId);
    }
}
