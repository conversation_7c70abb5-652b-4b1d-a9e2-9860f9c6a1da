package com.div.education.repository;

import com.div.education.entity.Course;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 课程数据访问层
 */
@Repository
public interface CourseRepository extends JpaRepository<Course, Long> {

    /**
     * 根据状态查找课程
     */
    Page<Course> findByStatus(Course.CourseStatus status, Pageable pageable);

    /**
     * 根据分类查找课程
     */
    Page<Course> findByCategory(String category, Pageable pageable);

    /**
     * 根据难度级别查找课程
     */
    Page<Course> findByLevel(String level, Pageable pageable);

    /**
     * 根据教师查找课程
     */
    Page<Course> findByTeacherId(Long teacherId, Pageable pageable);

    /**
     * 根据关键词搜索课程
     */
    @Query("SELECT c FROM Course c WHERE c.title LIKE %:keyword% OR c.description LIKE %:keyword% OR c.instructor LIKE %:keyword%")
    Page<Course> searchCourses(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据分类和级别查找课程
     */
    Page<Course> findByCategoryAndLevel(String category, String level, Pageable pageable);

    /**
     * 获取热门课程（按学生数量排序）
     */
    @Query("SELECT c FROM Course c WHERE c.status = 'PUBLISHED' ORDER BY c.studentCount DESC")
    List<Course> findPopularCourses(Pageable pageable);

    /**
     * 获取最新课程
     */
    @Query("SELECT c FROM Course c WHERE c.status = 'PUBLISHED' ORDER BY c.createdAt DESC")
    List<Course> findLatestCourses(Pageable pageable);

    /**
     * 获取推荐课程（按评分排序）
     */
    @Query("SELECT c FROM Course c WHERE c.status = 'PUBLISHED' AND c.rating >= :minRating ORDER BY c.rating DESC")
    List<Course> findRecommendedCourses(@Param("minRating") Double minRating, Pageable pageable);
}
