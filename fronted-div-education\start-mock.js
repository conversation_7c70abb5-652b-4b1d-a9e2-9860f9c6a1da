#!/usr/bin/env node

// Mock服务器启动脚本
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import { spawn } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🎭 启动DIV教育平台Mock服务器...')
console.log('📁 工作目录:', __dirname)

// 启动mock服务器
const mockServer = spawn('node', [join(__dirname, 'src/mock/server.js')], {
  stdio: 'inherit',
  cwd: __dirname
})

mockServer.on('error', (error) => {
  console.error('❌ Mock服务器启动失败:', error.message)
  process.exit(1)
})

mockServer.on('close', (code) => {
  console.log(`🎭 Mock服务器已停止 (退出码: ${code})`)
  process.exit(code)
})

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在停止Mock服务器...')
  mockServer.kill('SIGINT')
})

process.on('SIGTERM', () => {
  console.log('\n🛑 正在停止Mock服务器...')
  mockServer.kill('SIGTERM')
})
