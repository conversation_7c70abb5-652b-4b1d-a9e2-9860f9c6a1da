<template>
  <div class="create-post-page">
    <div class="container">
      <div class="page-header">
        <h1>发布帖子</h1>
        <p>分享你的想法和经验</p>
      </div>
      
      <div class="create-form">
        <el-form
          ref="postFormRef"
          :model="postForm"
          :rules="postRules"
          label-width="80px"
        >
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="postForm.title"
              placeholder="请输入帖子标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="内容" prop="content">
            <el-input
              v-model="postForm.content"
              type="textarea"
              :rows="10"
              placeholder="请输入帖子内容..."
              maxlength="5000"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="标签" prop="tags">
            <el-select
              v-model="postForm.tags"
              multiple
              filterable
              allow-create
              placeholder="选择或输入标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in commonTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="图片">
            <el-upload
              v-model:file-list="fileList"
              action="#"
              list-type="picture-card"
              :auto-upload="false"
              :limit="3"
              accept="image/*"
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  最多上传3张图片，每张不超过2MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
          
          <el-form-item>
            <div class="form-actions">
              <el-button @click="$router.back()">取消</el-button>
              <el-button type="primary" :loading="submitting" @click="submitPost">
                发布帖子
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useCommunityStore } from '../stores/community.js'
import { useUserStore } from '../stores/user.js'
import { ElMessage } from 'element-plus'

const router = useRouter()
const communityStore = useCommunityStore()
const userStore = useUserStore()

const postFormRef = ref()
const submitting = ref(false)
const fileList = ref([])

// 常用标签
const commonTags = [
  'Vue', 'React', 'JavaScript', 'TypeScript', 'Node.js',
  'Python', 'Java', 'Spring Boot', '前端开发', '后端开发',
  '学习心得', '项目分享', '技术讨论', '求助', '经验分享'
]

// 表单数据
const postForm = reactive({
  title: '',
  content: '',
  tags: []
})

// 表单验证规则
const postRules = {
  title: [
    { required: true, message: '请输入帖子标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在5到100个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入帖子内容', trigger: 'blur' },
    { min: 10, max: 5000, message: '内容长度在10到5000个字符', trigger: 'blur' }
  ],
  tags: [
    { type: 'array', max: 5, message: '最多选择5个标签', trigger: 'change' }
  ]
}

// 提交帖子
const submitPost = async () => {
  if (!postFormRef.value) return
  
  try {
    await postFormRef.value.validate()
    submitting.value = true
    
    // 处理图片上传（这里只是模拟）
    const images = fileList.value.map(file => {
      // 在实际项目中，这里应该上传图片到服务器并返回URL
      return 'https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png'
    })
    
    const postData = {
      title: postForm.title,
      content: postForm.content,
      tags: postForm.tags,
      images: images,
      author: {
        id: userStore.user.id,
        username: userStore.user.username,
        avatar: userStore.user.avatar,
        level: userStore.user.level
      }
    }
    
    await communityStore.createPost(postData)
    
    ElMessage.success('帖子发布成功')
    router.push('/community')
    
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('发布失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.create-post-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 16px;
}

.create-form {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .create-form {
    padding: 20px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
