<template>
  <div class="news-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>简讯</h1>
        <p>获取最新的技术资讯和行业动态</p>
      </div>

      <!-- 简讯列表 -->
      <div class="news-section">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        
        <div v-else-if="newsList.length === 0" class="empty-container">
          <el-empty description="暂无简讯数据" />
        </div>
        
        <div v-else class="news-grid">
          <div
            v-for="item in newsList"
            :key="item.id"
            class="news-card"
            @click="goToNewsDetail(item.id)"
          >
            <div class="news-image">
              <img :src="item.coverImage" :alt="item.title" />
              <span v-if="item.isHot" class="hot-badge">热门</span>
            </div>
            
            <div class="news-content">
              <div class="news-category">{{ item.category }}</div>
              <h3 class="news-title">{{ item.title }}</h3>
              <p class="news-summary">{{ item.summary }}</p>
              
              <div class="news-meta">
                <span class="read-time">
                  <el-icon><Clock /></el-icon>
                  {{ item.readTime }}分钟阅读
                </span>
                <span class="views">
                  <el-icon><View /></el-icon>
                  {{ formatNumber(item.viewsCount) }}
                </span>
                <span class="publish-time">
                  {{ formatTime(item.publishedAt) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { newsAPI } from '../mock/api.js'

const router = useRouter()

const loading = ref(false)
const newsList = ref([])

// 跳转到简讯详情
const goToNewsDetail = (newsId) => {
  router.push(`/news/${newsId}`)
}

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 格式化时间
const formatTime = (timeString) => {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now - time
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

// 获取简讯列表
const fetchNews = async () => {
  loading.value = true
  try {
    const response = await newsAPI.getNews()
    newsList.value = response.data
  } catch (error) {
    console.error('获取简讯失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await fetchNews()
})
</script>

<style scoped>
.news-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 16px;
  color: #606266;
}

.news-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container,
.empty-container {
  padding: 40px 0;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.news-card {
  border: 1px solid #ebeef5;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.news-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.news-card:hover .news-image img {
  transform: scale(1.05);
}

.hot-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #f56c6c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.news-content {
  padding: 20px;
}

.news-category {
  display: inline-block;
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 12px;
}

.news-title {
  font-size: 18px;
  color: #303133;
  margin-bottom: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
  flex-wrap: wrap;
  gap: 10px;
}

.news-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.read-time,
.views {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 24px;
  }
  
  .news-section {
    padding: 20px;
  }
  
  .news-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .news-image {
    height: 160px;
  }
  
  .news-content {
    padding: 15px;
  }
  
  .news-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
