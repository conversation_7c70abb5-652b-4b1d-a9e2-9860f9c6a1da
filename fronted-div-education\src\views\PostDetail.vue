<template>
  <div class="post-detail-page">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>
      
      <div v-else-if="!post" class="error-container">
        <el-result
          icon="warning"
          title="帖子不存在"
          sub-title="抱歉，您访问的帖子不存在或已被删除"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/community')">
              返回社区
            </el-button>
          </template>
        </el-result>
      </div>
      
      <div v-else class="post-content">
        <!-- 帖子内容 -->
        <div class="post-main">
          <div class="post-header">
            <h1 class="post-title">{{ post.title }}</h1>
            <div class="post-meta">
              <div class="author-info">
                <el-avatar :src="post.author.avatar" :size="40" />
                <div class="author-details">
                  <h4>{{ post.author.username }}</h4>
                  <span class="author-level">{{ post.author.level }}</span>
                </div>
              </div>
              <div class="post-time">
                发布于 {{ formatTime(post.createdAt) }}
              </div>
            </div>
          </div>
          
          <div class="post-body">
            <div class="post-content-text">
              {{ post.content }}
            </div>
            
            <!-- 帖子图片 -->
            <div v-if="post.images && post.images.length > 0" class="post-images">
              <img
                v-for="(image, index) in post.images"
                :key="index"
                :src="image"
                :alt="`图片${index + 1}`"
                class="post-image"
                @click="previewImage(image)"
              />
            </div>
            
            <!-- 帖子标签 -->
            <div class="post-tags">
              <el-tag
                v-for="tag in post.tags"
                :key="tag"
                size="large"
                type="info"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
          
          <!-- 帖子操作 -->
          <div class="post-actions">
            <el-button
              :type="post.isLiked ? 'primary' : 'default'"
              @click="handleLike"
            >
              <el-icon><component :is="post.isLiked ? 'StarFilled' : 'Star'" /></el-icon>
              点赞 ({{ post.likesCount }})
            </el-button>
            <el-button>
              <el-icon><ChatDotRound /></el-icon>
              评论 ({{ post.commentsCount }})
            </el-button>
            <el-button>
              <el-icon><Share /></el-icon>
              分享
            </el-button>
          </div>
        </div>
        
        <!-- 评论区 -->
        <div class="comments-section">
          <h3>评论 ({{ comments.length }})</h3>
          
          <!-- 发表评论 -->
          <div v-if="userStore.isLoggedIn" class="comment-form">
            <div class="comment-input">
              <el-avatar :src="userStore.userAvatar" :size="32" />
              <el-input
                v-model="newComment"
                type="textarea"
                :rows="3"
                placeholder="写下你的评论..."
                class="comment-textarea"
              />
            </div>
            <div class="comment-actions">
              <el-button type="primary" @click="submitComment">
                发表评论
              </el-button>
            </div>
          </div>
          
          <div v-else class="login-prompt">
            <p>请 <router-link to="/login">登录</router-link> 后发表评论</p>
          </div>
          
          <!-- 评论列表 -->
          <div class="comments-list">
            <div
              v-for="comment in comments"
              :key="comment.id"
              class="comment-item"
            >
              <el-avatar :src="comment.user.avatar" :size="36" />
              <div class="comment-content">
                <div class="comment-header">
                  <h5>{{ comment.user.username }}</h5>
                  <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                </div>
                <p class="comment-text">{{ comment.content }}</p>
              </div>
            </div>
          </div>
          
          <div v-if="comments.length === 0" class="empty-comments">
            <el-empty description="暂无评论，快来发表第一条评论吧！" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCommunityStore } from '../stores/community.js'
import { useUserStore } from '../stores/user.js'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const communityStore = useCommunityStore()
const userStore = useUserStore()

const loading = ref(true)
const post = ref(null)
const comments = ref([])
const newComment = ref('')

// 格式化时间
const formatTime = (timeString) => {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now - time
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

// 处理点赞
const handleLike = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    await communityStore.likePost(post.value.id)
    post.value = communityStore.currentPost
    ElMessage.success(post.value.isLiked ? '点赞成功' : '取消点赞')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 预览图片
const previewImage = (imageUrl) => {
  // 这里可以实现图片预览功能
  ElMessage.info('图片预览功能')
}

// 提交评论
const submitComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  try {
    const commentData = {
      postId: post.value.id,
      user: {
        id: userStore.user.id,
        username: userStore.user.username,
        avatar: userStore.user.avatar
      },
      content: newComment.value.trim(),
      parentId: null
    }

    await communityStore.createComment(commentData)
    
    // 重新获取评论列表
    await fetchComments()
    
    // 清空输入框
    newComment.value = ''
    
    ElMessage.success('评论发表成功')
  } catch (error) {
    ElMessage.error('评论发表失败')
  }
}

// 获取帖子详情
const fetchPostDetail = async () => {
  const postId = parseInt(route.params.id)
  
  try {
    loading.value = true
    await communityStore.fetchPostDetail(postId)
    post.value = communityStore.currentPost
  } catch (error) {
    console.error('获取帖子详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取评论列表
const fetchComments = async () => {
  const postId = parseInt(route.params.id)
  
  try {
    await communityStore.fetchComments(postId)
    comments.value = communityStore.comments
  } catch (error) {
    console.error('获取评论失败:', error)
  }
}

// 页面初始化
onMounted(async () => {
  await fetchPostDetail()
  await fetchComments()
})
</script>

<style scoped>
.post-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.loading-container,
.error-container {
  padding: 40px 0;
}

.post-main {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.post-header {
  margin-bottom: 30px;
}

.post-title {
  font-size: 28px;
  color: #303133;
  margin-bottom: 20px;
  line-height: 1.3;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid #f0f2f5;
  border-bottom: 1px solid #f0f2f5;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.author-level {
  font-size: 12px;
  color: #909399;
}

.post-time {
  color: #909399;
  font-size: 14px;
}

.post-body {
  margin-bottom: 30px;
}

.post-content-text {
  color: #303133;
  line-height: 1.8;
  font-size: 16px;
  margin-bottom: 20px;
  white-space: pre-wrap;
}

.post-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.post-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s;
}

.post-image:hover {
  transform: scale(1.02);
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.post-actions {
  display: flex;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #f0f2f5;
}

.comments-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.comments-section h3 {
  color: #303133;
  margin-bottom: 20px;
}

.comment-form {
  margin-bottom: 30px;
}

.comment-input {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
}

.comment-textarea {
  flex: 1;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
}

.login-prompt {
  text-align: center;
  padding: 20px;
  color: #606266;
  margin-bottom: 30px;
}

.login-prompt a {
  color: #409eff;
  text-decoration: none;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  display: flex;
  gap: 12px;
  padding: 15px 0;
  border-bottom: 1px solid #f0f2f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.comment-header h5 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

.comment-time {
  color: #909399;
  font-size: 12px;
}

.comment-text {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.empty-comments {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .post-main,
  .comments-section {
    padding: 20px;
  }
  
  .post-title {
    font-size: 24px;
  }
  
  .post-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .post-images {
    grid-template-columns: 1fr;
  }
  
  .post-actions {
    flex-wrap: wrap;
  }
  
  .comment-input {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
