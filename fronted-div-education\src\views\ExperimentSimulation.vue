<template>
  <div class="experiment-simulation-page">
    <div class="simulation-container">
      <!-- 实验头部 -->
      <div class="simulation-header">
        <div class="experiment-info">
          <h2>{{ experiment?.title }}</h2>
          <div class="experiment-meta">
            <span class="subject-badge" :class="experiment?.subject?.toLowerCase()">
              {{ experiment?.subject }}
            </span>
            <span class="timer">
              <el-icon><Clock /></el-icon>
              {{ formatTime(elapsedTime) }}
            </span>
          </div>
        </div>
        
        <div class="simulation-controls">
          <el-button @click="toggleFullscreen" :type="isFullscreen ? 'warning' : 'info'">
            <el-icon><component :is="isFullscreen ? 'Minus' : 'FullScreen'" /></el-icon>
            {{ isFullscreen ? '退出全屏' : '全屏模式' }}
          </el-button>
          <el-button @click="pauseExperiment" :disabled="!isRunning">
            <el-icon><VideoPause /></el-icon>
            暂停
          </el-button>
          <el-button @click="resetExperiment">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="completeExperiment" :disabled="!canComplete">
            <el-icon><Check /></el-icon>
            完成实验
          </el-button>
        </div>
      </div>

      <!-- 实验主体 -->
      <div class="simulation-body">
        <!-- 实验画布区域 -->
        <div class="simulation-canvas">
          <div class="canvas-container">
            <!-- 这里是实验模拟的主要区域 -->
            <div v-if="experiment?.id === 1" class="pendulum-experiment">
              <h3>单摆运动实验</h3>
              <div class="pendulum-setup">
                <div class="pendulum-container">
                  <div 
                    class="pendulum-string" 
                    :style="{ height: pendulumLength + 'px' }"
                  ></div>
                  <div 
                    class="pendulum-bob"
                    :style="{ 
                      left: pendulumPosition + 'px',
                      top: pendulumLength + 'px'
                    }"
                  ></div>
                </div>
              </div>
              
              <div class="experiment-controls">
                <div class="control-group">
                  <label>摆长 (cm):</label>
                  <el-slider
                    v-model="pendulumLength"
                    :min="50"
                    :max="200"
                    :step="10"
                    show-input
                    @change="updatePendulum"
                  />
                </div>
                
                <div class="control-group">
                  <label>初始角度 (度):</label>
                  <el-slider
                    v-model="initialAngle"
                    :min="5"
                    :max="30"
                    :step="1"
                    show-input
                    @change="updatePendulum"
                  />
                </div>
                
                <div class="control-buttons">
                  <el-button type="primary" @click="startPendulum" :disabled="isRunning">
                    开始摆动
                  </el-button>
                  <el-button @click="stopPendulum" :disabled="!isRunning">
                    停止
                  </el-button>
                  <el-button @click="measurePeriod" :disabled="!isRunning">
                    测量周期
                  </el-button>
                </div>
              </div>
            </div>
            
            <div v-else-if="experiment?.id === 2" class="titration-experiment">
              <h3>酸碱中和滴定实验</h3>
              <div class="titration-setup">
                <div class="titration-apparatus">
                  <div class="burette">
                    <div class="burette-scale"></div>
                    <div 
                      class="solution-level" 
                      :style="{ height: buretteLevel + '%' }"
                    ></div>
                  </div>
                  <div class="conical-flask">
                    <div 
                      class="flask-solution"
                      :style="{ backgroundColor: flaskColor }"
                    ></div>
                  </div>
                </div>
              </div>
              
              <div class="experiment-controls">
                <div class="control-group">
                  <label>NaOH浓度 (mol/L):</label>
                  <el-input-number
                    v-model="naohConcentration"
                    :min="0.01"
                    :max="1.0"
                    :step="0.01"
                    :precision="2"
                  />
                </div>
                
                <div class="control-group">
                  <label>HCl体积 (mL):</label>
                  <el-input-number
                    v-model="hclVolume"
                    :min="10"
                    :max="50"
                    :step="1"
                  />
                </div>
                
                <div class="control-buttons">
                  <el-button type="primary" @click="startTitration" :disabled="isRunning">
                    开始滴定
                  </el-button>
                  <el-button @click="addDrop" :disabled="!isRunning">
                    滴加一滴
                  </el-button>
                  <el-button @click="stopTitration" :disabled="!isRunning">
                    停止滴定
                  </el-button>
                </div>
              </div>
            </div>
            
            <div v-else class="default-experiment">
              <div class="experiment-placeholder">
                <el-icon size="80"><Experiment /></el-icon>
                <h3>实验模拟界面</h3>
                <p>{{ experiment?.title }}</p>
                <p>实验模拟功能正在开发中...</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据记录区域 -->
        <div class="data-panel">
          <div class="panel-header">
            <h3>实验数据</h3>
            <el-button size="small" @click="clearData">清空数据</el-button>
          </div>
          
          <div class="data-table">
            <el-table :data="experimentData" size="small" max-height="300">
              <el-table-column prop="measurement" label="测量项" width="120" />
              <el-table-column prop="value" label="数值" width="100" />
              <el-table-column prop="unit" label="单位" width="80" />
              <el-table-column prop="time" label="时间" />
            </el-table>
          </div>
          
          <div class="data-analysis">
            <h4>数据分析</h4>
            <div class="analysis-content">
              <p v-if="experimentData.length === 0">暂无数据</p>
              <div v-else>
                <p>已记录 {{ experimentData.length }} 组数据</p>
                <p v-if="averageValue">平均值: {{ averageValue }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { experimentAPI } from '../mock/api.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const experiment = ref(null)
const isRunning = ref(false)
const elapsedTime = ref(0)
const experimentData = ref([])
const canComplete = ref(false)
const isFullscreen = ref(false)

// 单摆实验相关状态
const pendulumLength = ref(100)
const initialAngle = ref(15)
const pendulumPosition = ref(0)
const pendulumAnimation = ref(null)

// 滴定实验相关状态
const naohConcentration = ref(0.1)
const hclVolume = ref(25)
const buretteLevel = ref(100)
const flaskColor = ref('#ffcccc')
const dropsAdded = ref(0)

// 计时器
let timer = null

// 计算平均值
const averageValue = computed(() => {
  if (experimentData.value.length === 0) return null
  const sum = experimentData.value.reduce((acc, item) => acc + parseFloat(item.value), 0)
  return (sum / experimentData.value.length).toFixed(2)
})

// 格式化时间
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 开始计时
const startTimer = () => {
  timer = setInterval(() => {
    elapsedTime.value++
  }, 1000)
}

// 停止计时
const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 切换全屏模式
const toggleFullscreen = () => {
  const element = document.documentElement

  if (!isFullscreen.value) {
    // 进入全屏
    if (element.requestFullscreen) {
      element.requestFullscreen()
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen()
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen()
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen()
    }
    isFullscreen.value = true
    ElMessage.success('已进入全屏免打扰模式')
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen()
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen()
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen()
    }
    isFullscreen.value = false
    ElMessage.info('已退出全屏模式')
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  const isCurrentlyFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  )
  isFullscreen.value = isCurrentlyFullscreen
}

// 暂停实验
const pauseExperiment = () => {
  isRunning.value = false
  stopTimer()
  stopPendulum()
  stopTitration()
}

// 重置实验
const resetExperiment = async () => {
  try {
    await ElMessageBox.confirm('确定要重置实验吗？所有数据将被清空。', '确认重置', {
      type: 'warning'
    })
    
    isRunning.value = false
    elapsedTime.value = 0
    experimentData.value = []
    canComplete.value = false
    stopTimer()
    
    // 重置实验特定状态
    if (experiment.value?.id === 1) {
      stopPendulum()
      pendulumPosition.value = 0
    } else if (experiment.value?.id === 2) {
      buretteLevel.value = 100
      flaskColor.value = '#ffcccc'
      dropsAdded.value = 0
    }
    
    ElMessage.success('实验已重置')
  } catch {
    // 用户取消
  }
}

// 完成实验
const completeExperiment = async () => {
  try {
    await ElMessageBox.confirm('确定完成实验吗？', '确认完成', {
      type: 'info'
    })
    
    pauseExperiment()
    
    // 提交实验数据
    const finalData = {
      measurements: experimentData.value,
      totalTime: elapsedTime.value,
      analysis: '实验完成，数据记录完整'
    }
    
    // 这里应该调用API提交数据
    ElMessage.success('实验完成！正在计算成绩...')
    
    setTimeout(() => {
      const score = Math.floor(Math.random() * 30) + 70 // 70-100分
      ElMessage.success(`实验完成！您的得分是：${score}分`)
      router.push(`/lab/experiment/${experiment.value.id}`)
    }, 2000)
    
  } catch {
    // 用户取消
  }
}

// 单摆实验相关方法
const updatePendulum = () => {
  if (!isRunning.value) {
    pendulumPosition.value = Math.sin(initialAngle.value * Math.PI / 180) * pendulumLength.value * 0.5
  }
}

const startPendulum = () => {
  isRunning.value = true
  startTimer()
  
  // 简单的单摆动画
  let angle = initialAngle.value
  let velocity = 0
  const gravity = 9.8
  const dampening = 0.999
  
  const animate = () => {
    if (!isRunning.value) return
    
    const acceleration = -(gravity / (pendulumLength.value / 100)) * Math.sin(angle * Math.PI / 180)
    velocity += acceleration * 0.1
    velocity *= dampening
    angle += velocity
    
    pendulumPosition.value = Math.sin(angle * Math.PI / 180) * pendulumLength.value * 0.5
    
    pendulumAnimation.value = requestAnimationFrame(animate)
  }
  
  animate()
}

const stopPendulum = () => {
  if (pendulumAnimation.value) {
    cancelAnimationFrame(pendulumAnimation.value)
    pendulumAnimation.value = null
  }
}

const measurePeriod = () => {
  const period = 2 * Math.PI * Math.sqrt((pendulumLength.value / 100) / 9.8)
  addDataPoint('周期', period.toFixed(2), 's')
  canComplete.value = experimentData.value.length >= 3
}

// 滴定实验相关方法
const startTitration = () => {
  isRunning.value = true
  startTimer()
  ElMessage.info('开始滴定，请点击"滴加一滴"按钮进行滴定')
}

const stopTitration = () => {
  isRunning.value = false
  stopTimer()
}

const addDrop = () => {
  if (!isRunning.value) return
  
  dropsAdded.value++
  buretteLevel.value = Math.max(0, 100 - dropsAdded.value * 2)
  
  // 模拟颜色变化
  const ratio = dropsAdded.value / 50
  if (ratio < 0.9) {
    flaskColor.value = '#ffcccc'
  } else if (ratio < 1.0) {
    flaskColor.value = '#ffdddd'
  } else {
    flaskColor.value = '#ccffcc'
    ElMessage.success('到达滴定终点！')
    addDataPoint('消耗体积', (dropsAdded.value * 0.05).toFixed(2), 'mL')
    canComplete.value = true
  }
}

// 添加数据点
const addDataPoint = (measurement, value, unit) => {
  experimentData.value.push({
    measurement,
    value,
    unit,
    time: new Date().toLocaleTimeString()
  })
}

// 清空数据
const clearData = () => {
  experimentData.value = []
  canComplete.value = false
}

// 获取实验详情
const fetchExperimentDetail = async () => {
  const experimentId = parseInt(route.params.id)
  
  try {
    const response = await experimentAPI.getExperimentDetail(experimentId)
    experiment.value = response.data
    
    // 开始实验记录
    await experimentAPI.startExperiment(userStore.user.id, experimentId)
  } catch (error) {
    console.error('获取实验详情失败:', error)
    ElMessage.error('获取实验详情失败')
    router.push('/lab')
  }
}

// 页面初始化
onMounted(async () => {
  await fetchExperimentDetail()
  updatePendulum()

  // 添加全屏事件监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
})

// 页面销毁
onUnmounted(() => {
  stopTimer()
  stopPendulum()

  // 移除全屏事件监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
})
</script>

<style scoped>
.experiment-simulation-page {
  min-height: 100vh;
  background: #1a1a1a;
  color: white;
  padding: 0;
}

.simulation-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.simulation-header {
  background: #2d2d2d;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #404040;
}

.experiment-info h2 {
  margin: 0 0 8px 0;
  color: white;
  font-size: 20px;
}

.experiment-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.subject-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.subject-badge.物理 {
  background: #667eea;
}

.subject-badge.化学 {
  background: #f5576c;
}

.timer {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #67c23a;
  font-weight: bold;
}

.simulation-controls {
  display: flex;
  gap: 10px;
}

.simulation-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.simulation-canvas {
  flex: 1;
  background: #f5f7fa;
  color: #303133;
  padding: 20px;
  overflow-y: auto;
}

.canvas-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pendulum-experiment,
.titration-experiment {
  height: 100%;
}

.pendulum-experiment h3,
.titration-experiment h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.pendulum-setup {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  position: relative;
}

.pendulum-container {
  position: relative;
  width: 400px;
  height: 250px;
}

.pendulum-string {
  position: absolute;
  left: 50%;
  top: 20px;
  width: 2px;
  background: #333;
  transform-origin: top center;
}

.pendulum-bob {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #f56c6c;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.1s ease;
}

.titration-setup {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.titration-apparatus {
  display: flex;
  align-items: flex-end;
  gap: 50px;
}

.burette {
  width: 30px;
  height: 200px;
  background: #e6e6e6;
  border-radius: 15px;
  position: relative;
  overflow: hidden;
}

.solution-level {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #409eff;
  transition: height 0.3s ease;
}

.conical-flask {
  width: 80px;
  height: 100px;
  background: #e6e6e6;
  border-radius: 0 0 40px 40px;
  position: relative;
  overflow: hidden;
}

.flask-solution {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  transition: background-color 0.3s ease;
}

.experiment-controls {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  color: #606266;
  font-weight: 500;
}

.control-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.default-experiment {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.experiment-placeholder {
  text-align: center;
  color: #909399;
}

.experiment-placeholder h3 {
  margin: 20px 0 10px 0;
  color: #606266;
}

.data-panel {
  width: 350px;
  background: #2d2d2d;
  border-left: 1px solid #404040;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: white;
}

.data-table {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.data-analysis {
  padding: 20px;
  border-top: 1px solid #404040;
}

.data-analysis h4 {
  margin: 0 0 10px 0;
  color: white;
}

.analysis-content {
  color: #ccc;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simulation-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .simulation-controls {
    justify-content: center;
  }
  
  .simulation-body {
    flex-direction: column;
  }
  
  .data-panel {
    width: 100%;
    height: 300px;
  }
  
  .control-buttons {
    justify-content: center;
  }
}
</style>
