<template>
  <el-header class="app-header">
    <div class="header-content">
      <!-- Logo和标题 -->
      <div class="logo-section">
        <router-link to="/" class="logo-link">
          <el-icon class="logo-icon" size="32">
            <School />
          </el-icon>
          <span class="logo-text">DIV教育平台</span>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <el-menu
        :default-active="activeIndex"
        class="header-menu"
        mode="horizontal"
        router
        @select="handleSelect"
      >
        <el-menu-item index="/">首页</el-menu-item>
        <el-menu-item index="/learning">学习中心</el-menu-item>
        <el-menu-item index="/community">交流社区</el-menu-item>
        <el-menu-item index="/lab">仿真实验</el-menu-item>
        <el-menu-item index="/news">简讯</el-menu-item>
        <el-menu-item index="/groups">学习小组</el-menu-item>
        <el-menu-item v-if="userStore.isLoggedIn" index="/analytics">数据分析</el-menu-item>
      </el-menu>

      <!-- 搜索框 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索课程、帖子..."
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="search-icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <!-- 用户区域 -->
      <div class="user-section">
        <template v-if="userStore.isLoggedIn">
          <el-dropdown @command="handleUserCommand">
            <span class="user-info">
              <el-avatar :src="userStore.userAvatar" :size="32" />
              <span class="username">{{ userStore.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="logout" divided>
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button type="primary" @click="$router.push('/login')">
            登录
          </el-button>
          <el-button @click="$router.push('/register')">
            注册
          </el-button>
        </template>
      </div>
    </div>
  </el-header>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../../stores/user.js'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const searchKeyword = ref('')

// 当前激活的菜单项
const activeIndex = computed(() => {
  return route.path
})

// 处理菜单选择
const handleSelect = (key) => {
  console.log('选择菜单:', key)
}

// 处理搜索
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/learning',
      query: { keyword: searchKeyword.value.trim() }
    })
  }
}

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('退出登录成功')
      router.push('/')
      break
  }
}
</script>

<style scoped>
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(228, 231, 237, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo-section {
  margin-right: 50px;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.logo-link:hover {
  transform: scale(1.05);
}

.logo-icon {
  margin-right: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-text {
  font-size: 22px;
  font-weight: 700;
  letter-spacing: -0.5px;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
  white-space: nowrap;
}

.header-menu {
  flex: 1;
  border-bottom: none;
  background: transparent;
}

.header-menu .el-menu-item {
  border-radius: 8px;
  margin: 0 4px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.header-menu .el-menu-item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
}

.header-menu .el-menu-item.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.search-section {
  margin: 0 25px;
}

.search-input {
  width: 320px;
}

.search-input .el-input__wrapper {
  border-radius: 25px;
  background: rgba(245, 247, 250, 0.8);
  border: 1px solid rgba(228, 231, 237, 0.5);
  transition: all 0.3s ease;
}

.search-input .el-input__wrapper:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.search-icon {
  cursor: pointer;
  color: #909399;
  transition: all 0.3s ease;
}

.search-icon:hover {
  color: #667eea;
  transform: scale(1.1);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-section .el-button {
  border-radius: 20px;
  font-weight: 500;
  padding: 8px 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
  background: rgba(245, 247, 250, 0.5);
}

.user-info:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.username {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .logo-section {
    margin-right: 20px;
  }

  .logo-text {
    font-size: 18px;
  }

  .search-section {
    display: none;
  }

  .header-menu .el-menu-item {
    font-size: 14px;
    padding: 0 10px;
  }
}
</style>
