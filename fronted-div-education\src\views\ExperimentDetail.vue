<template>
  <div class="experiment-detail-page">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>
      
      <div v-else-if="!experiment" class="error-container">
        <el-result
          icon="warning"
          title="实验不存在"
          sub-title="抱歉，您访问的实验不存在或已被删除"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/lab')">
              返回实验室
            </el-button>
          </template>
        </el-result>
      </div>
      
      <div v-else class="experiment-content">
        <!-- 实验头部信息 -->
        <div class="experiment-header">
          <div class="experiment-cover">
            <img :src="experiment.coverImage" :alt="experiment.title" />
            <div class="play-overlay">
              <el-button type="primary" size="large" @click="startExperiment">
                <el-icon size="24"><VideoPlay /></el-icon>
                开始实验
              </el-button>
            </div>
          </div>
          
          <div class="experiment-info">
            <div class="subject-badge" :class="experiment.subject.toLowerCase()">
              {{ experiment.subject }}
            </div>
            <h1 class="experiment-title">{{ experiment.title }}</h1>
            <p class="experiment-description">{{ experiment.description }}</p>
            
            <!-- 实验统计 -->
            <div class="experiment-stats">
              <div class="stat-item">
                <el-icon><Clock /></el-icon>
                <span>{{ experiment.duration }} 分钟</span>
              </div>
              <div class="stat-item">
                <el-icon><User /></el-icon>
                <span>{{ experiment.completedCount }} 人完成</span>
              </div>
              <div class="stat-item">
                <el-icon><Star /></el-icon>
                <span>{{ experiment.rating }} 评分</span>
              </div>
              <div class="stat-item">
                <el-icon><Trophy /></el-icon>
                <span>{{ getDifficultyText(experiment.difficulty) }}</span>
              </div>
            </div>
            
            <!-- 实验标签 -->
            <div class="experiment-tags">
              <el-tag 
                v-for="tag in experiment.tags" 
                :key="tag" 
                size="large" 
                type="info"
              >
                {{ tag }}
              </el-tag>
            </div>
            
            <!-- 操作按钮 -->
            <div class="experiment-actions">
              <el-button type="primary" size="large" @click="startExperiment">
                <el-icon><VideoPlay /></el-icon>
                开始实验
              </el-button>
              <el-button size="large" @click="toggleFavorite">
                <el-icon><Star /></el-icon>
                收藏实验
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 实验详情标签页 -->
        <div class="experiment-tabs">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="实验目标" name="objectives">
              <div class="objectives-section">
                <h3>学习目标</h3>
                <ul class="objectives-list">
                  <li v-for="objective in experiment.objectives" :key="objective">
                    <el-icon class="check-icon"><Check /></el-icon>
                    {{ objective }}
                  </li>
                </ul>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="实验器材" name="equipment">
              <div class="equipment-section">
                <h3>所需器材</h3>
                <div class="equipment-grid">
                  <div
                    v-for="item in experiment.equipment"
                    :key="item"
                    class="equipment-item"
                  >
                    <el-icon class="equipment-icon"><Box /></el-icon>
                    <span>{{ item }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="实验步骤" name="steps">
              <div class="steps-section">
                <h3>实验步骤</h3>
                <div class="steps-list">
                  <div
                    v-for="(step, index) in experiment.steps"
                    :key="index"
                    class="step-item"
                  >
                    <div class="step-number">{{ index + 1 }}</div>
                    <div class="step-content">{{ step }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="实验记录" name="records">
              <div class="records-section">
                <div v-if="!userStore.isLoggedIn" class="login-prompt">
                  <el-empty description="请登录查看实验记录">
                    <el-button type="primary" @click="$router.push('/login')">
                      立即登录
                    </el-button>
                  </el-empty>
                </div>
                
                <div v-else-if="experimentRecords.length === 0" class="empty-records">
                  <el-empty description="暂无实验记录，快来开始第一次实验吧！">
                    <el-button type="primary" @click="startExperiment">
                      开始实验
                    </el-button>
                  </el-empty>
                </div>
                
                <div v-else class="records-list">
                  <div
                    v-for="record in experimentRecords"
                    :key="record.id"
                    class="record-item"
                  >
                    <div class="record-header">
                      <div class="record-info">
                        <h5>第 {{ record.attempts }} 次尝试</h5>
                        <span class="record-status" :class="record.status">
                          {{ getStatusText(record.status) }}
                        </span>
                      </div>
                      <div class="record-score" v-if="record.score">
                        {{ record.score }} 分
                      </div>
                    </div>
                    <div class="record-meta">
                      <span>用时: {{ record.timeSpent }} 分钟</span>
                      <span>完成时间: {{ formatTime(record.completedAt || record.createdAt) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { experimentAPI } from '../mock/api.js'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const loading = ref(true)
const experiment = ref(null)
const experimentRecords = ref([])
const activeTab = ref('objectives')

// 获取难度文本
const getDifficultyText = (difficulty) => {
  const textMap = {
    'beginner': '初级',
    'intermediate': '中级',
    'advanced': '高级'
  }
  return textMap[difficulty] || '初级'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'pending': '待开始',
    'in_progress': '进行中',
    'completed': '已完成'
  }
  return textMap[status] || '未知'
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const time = new Date(timeString)
  return time.toLocaleString()
}

// 开始实验
const startExperiment = () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  router.push(`/lab/simulation/${experiment.value.id}`)
}

// 切换收藏状态
const toggleFavorite = () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  ElMessage.success('已收藏实验')
}

// 获取实验详情
const fetchExperimentDetail = async () => {
  const experimentId = parseInt(route.params.id)
  
  try {
    loading.value = true
    const response = await experimentAPI.getExperimentDetail(experimentId)
    experiment.value = response.data
  } catch (error) {
    console.error('获取实验详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取实验记录
const fetchExperimentRecords = async () => {
  if (!userStore.isLoggedIn) return
  
  try {
    const response = await experimentAPI.getUserExperimentRecords(userStore.user.id)
    const experimentId = parseInt(route.params.id)
    experimentRecords.value = response.data.filter(r => r.experimentId === experimentId)
  } catch (error) {
    console.error('获取实验记录失败:', error)
  }
}

// 页面初始化
onMounted(async () => {
  await fetchExperimentDetail()
  await fetchExperimentRecords()
})
</script>

<style scoped>
.experiment-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.loading-container,
.error-container {
  padding: 40px 0;
}

.experiment-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 30px;
}

.experiment-cover {
  position: relative;
  width: 400px;
  height: 250px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.experiment-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.experiment-info {
  flex: 1;
}

.subject-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  margin-bottom: 15px;
}

.subject-badge.物理 {
  background: #667eea;
}

.subject-badge.化学 {
  background: #f5576c;
}

.experiment-title {
  font-size: 28px;
  color: #303133;
  margin-bottom: 15px;
  line-height: 1.3;
}

.experiment-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 20px;
}

.experiment-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 25px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 14px;
}

.experiment-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 30px;
}

.experiment-actions {
  display: flex;
  gap: 15px;
}

.experiment-tabs {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.objectives-section h3,
.equipment-section h3,
.steps-section h3 {
  color: #303133;
  margin-bottom: 20px;
}

.objectives-list {
  list-style: none;
  padding: 0;
}

.objectives-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  color: #606266;
  line-height: 1.6;
}

.check-icon {
  color: #67c23a;
  font-size: 16px;
}

.equipment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.equipment-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #606266;
}

.equipment-icon {
  color: #409eff;
  font-size: 18px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  color: #606266;
  line-height: 1.6;
  padding-top: 5px;
}

.records-section {
  min-height: 300px;
}

.login-prompt,
.empty-records {
  padding: 40px 0;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.record-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.record-info h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.record-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.record-status.completed {
  background: #f0f9ff;
  color: #409eff;
}

.record-status.in_progress {
  background: #fef0e6;
  color: #e6a23c;
}

.record-score {
  font-size: 18px;
  font-weight: bold;
  color: #67c23a;
}

.record-meta {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .experiment-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .experiment-cover {
    width: 100%;
    height: 200px;
  }
  
  .experiment-title {
    font-size: 24px;
  }
  
  .experiment-stats {
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .experiment-actions {
    flex-direction: column;
  }
  
  .equipment-grid {
    grid-template-columns: 1fr;
  }
  
  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .record-meta {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
