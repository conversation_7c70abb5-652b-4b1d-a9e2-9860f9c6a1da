<template>
  <div class="profile-page">
    <div class="container">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <div class="user-avatar">
          <el-avatar :src="userStore.userAvatar" :size="80" />
          <el-button type="primary" size="small" class="edit-avatar-btn">
            更换头像
          </el-button>
        </div>
        
        <div class="user-info">
          <h2>{{ userStore.username }}</h2>
          <p class="user-email">{{ userStore.user?.email }}</p>
          <p class="user-level">{{ userStore.user?.level }}</p>
          <p class="join-date">加入时间：{{ formatDate(userStore.user?.joinDate) }}</p>
        </div>

        <div class="user-stats">
          <div class="stat-item">
            <div class="stat-number">{{ userStore.user?.learningHours || 0 }}</div>
            <div class="stat-label">学习时长(小时)</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStore.user?.completedCourses || 0 }}</div>
            <div class="stat-label">完成课程</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStore.user?.points || 0 }}</div>
            <div class="stat-label">学习积分</div>
          </div>
        </div>
      </div>

      <!-- 功能导航 -->
      <div class="profile-nav">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="学习记录" name="learning">
            <div class="learning-records">
              <div v-if="learningRecords.length === 0" class="empty-state">
                <el-empty description="暂无学习记录" />
              </div>
              <div v-else class="records-list">
                <div
                  v-for="record in learningRecords"
                  :key="record.id"
                  class="record-item"
                  @click="goToCourse(record.courseId)"
                >
                  <div class="record-info">
                    <h4>{{ getCourseTitle(record.courseId) }}</h4>
                    <p>最后学习：{{ formatTime(record.updatedAt) }}</p>
                  </div>
                  <div class="record-progress">
                    <el-progress :percentage="record.progress" />
                    <span class="progress-text">{{ record.progress }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="个人设置" name="settings">
            <div class="profile-settings">
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="profileForm.username" />
                </el-form-item>
                
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="profileForm.email" />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="handleUpdateProfile">
                    保存修改
                  </el-button>
                </el-form-item>
              </el-form>

              <el-divider />

              <h3>修改密码</h3>
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
              >
                <el-form-item label="当前密码" prop="currentPassword">
                  <el-input v-model="passwordForm.currentPassword" type="password" show-password />
                </el-form-item>
                
                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model="passwordForm.newPassword" type="password" show-password />
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="handleChangePassword">
                    修改密码
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { useCourseStore } from '../stores/course.js'
import { learningAPI } from '../mock/api.js'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const courseStore = useCourseStore()

const activeTab = ref('learning')
const learningRecords = ref([])

// 个人信息表单
const profileFormRef = ref()
const profileForm = reactive({
  username: '',
  email: ''
})

const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在2到20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 密码修改表单
const passwordFormRef = ref()
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 处理标签页切换
const handleTabClick = (tab) => {
  console.log('切换到标签页:', tab.name)
}

// 跳转到课程
const goToCourse = (courseId) => {
  router.push(`/learning/course/${courseId}`)
}

// 获取课程标题
const getCourseTitle = (courseId) => {
  const course = courseStore.courses.find(c => c.id === courseId)
  return course ? course.title : '未知课程'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const time = new Date(timeString)
  const now = new Date()
  const diff = now - time
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

// 更新个人信息
const handleUpdateProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    await userStore.updateProfile(profileForm)
    ElMessage.success('个人信息更新成功')
  } catch (error) {
    ElMessage.error('更新失败')
  }
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    // 这里应该调用修改密码的API
    ElMessage.success('密码修改成功')
    // 清空表单
    Object.keys(passwordForm).forEach(key => {
      passwordForm[key] = ''
    })
  } catch (error) {
    ElMessage.error('密码修改失败')
  }
}

// 获取学习记录
const fetchLearningRecords = async () => {
  if (!userStore.user?.id) return
  
  try {
    const response = await learningAPI.getUserLearningRecords(userStore.user.id)
    learningRecords.value = response.data
  } catch (error) {
    console.error('获取学习记录失败:', error)
  }
}

// 页面初始化
onMounted(async () => {
  // 初始化表单数据
  if (userStore.user) {
    profileForm.username = userStore.user.username
    profileForm.email = userStore.user.email
  }
  
  // 获取课程列表（用于显示课程标题）
  await courseStore.fetchCourses()
  
  // 获取学习记录
  await fetchLearningRecords()
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.user-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 30px;
}

.user-avatar {
  text-align: center;
}

.edit-avatar-btn {
  margin-top: 10px;
}

.user-info {
  flex: 1;
}

.user-info h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.user-email {
  color: #606266;
  margin-bottom: 5px;
}

.user-level {
  color: #409eff;
  font-weight: 500;
  margin-bottom: 5px;
}

.join-date {
  color: #909399;
  font-size: 14px;
}

.user-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.profile-nav {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.learning-records {
  min-height: 300px;
}

.empty-state {
  padding: 40px 0;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.record-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.record-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.record-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.record-progress {
  width: 200px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.profile-settings {
  max-width: 500px;
}

.profile-settings h3 {
  color: #303133;
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .user-stats {
    justify-content: center;
    gap: 20px;
  }

  .record-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .record-progress {
    width: 100%;
  }

  .profile-nav {
    padding: 20px;
  }
}
</style>
