import { defineStore } from 'pinia'
import { api } from '../api/index.js'

export const useCourseStore = defineStore('course', {
  state: () => ({
    // 课程列表
    courses: [],
    // 当前课程
    currentCourse: null,
    // 课程分类
    categories: [],
    // 学习记录
    learningRecords: {},
    // 学习进度
    learningProgress: {},
    // 加载状态
    loading: false,
    // 分页信息
    pagination: {
      page: 1,
      pageSize: 12,
      total: 0
    }
  }),

  getters: {
    // 根据分类ID获取分类名称
    getCategoryName: (state) => (categoryId) => {
      const category = state.categories.find(c => c.id === categoryId)
      return category ? category.name : '未知分类'
    },
    
    // 获取推荐课程
    recommendedCourses: (state) => {
      return state.courses
        .filter(course => course.rating >= 4.5)
        .sort((a, b) => b.studentsCount - a.studentsCount)
        .slice(0, 6)
    }
  },

  actions: {
    // 获取课程列表
    async fetchCourses(params = {}) {
      this.loading = true
      try {
        // 重置分页信息
        this.pagination.page = params.page || 1
        this.pagination.pageSize = params.pageSize || 12
        
        const response = await api.courses.getCourses(
          this.pagination.page - 1, // 后端使用0基索引
          this.pagination.pageSize,
          params.categoryId,
          params.keyword,
          params.difficulty
        )
        
        // 确保数据格式正确
        if (response.data) {
          this.courses = response.data.content || response.data.list || response.data || []
          this.pagination.total = response.data.totalElements || response.data.total || 0
          this.pagination.page = (response.data.number || response.data.page || 0) + 1
        } else {
          this.courses = []
          this.pagination.total = 0
        }
        
        return response
      } catch (error) {
        console.error('获取课程列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取课程分类
    async fetchCategories() {
      try {
        const response = await api.courses.getCategories()
        this.categories = response.data
        return response
      } catch (error) {
        console.error('获取课程分类失败:', error)
        throw error
      }
    },

    // 获取课程详情
    async fetchCourseDetail(courseId) {
      this.loading = true
      try {
        const response = await api.courses.getCourseById(courseId)
        this.currentCourse = response.data
        return response
      } catch (error) {
        console.error('获取课程详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 搜索课程
    async searchCourses(keyword) {
      return await this.fetchCourses({ keyword })
    },

    // 获取用户学习记录
    async fetchUserLearningRecords(userId) {
      try {
        const response = await api.learning.getLearningRecords(userId)
        // 将学习记录存储到状态中
        response.data.forEach(record => {
          const key = `${userId}-${record.courseId}`
          this.learningRecords[key] = record
        })
        return response
      } catch (error) {
        console.error('获取学习记录失败:', error)
        throw error
      }
    },

    // 记录学习进度
    async recordLearningProgress(progressData) {
      try {
        const response = await api.learning.recordProgress(progressData)
        
        // 更新本地学习进度
        const key = `${progressData.userId}-${progressData.courseId}`
        this.learningProgress[key] = response.data
        
        return response
      } catch (error) {
        console.error('记录学习进度失败:', error)
        throw error
      }
    },

    // 设置分页
    setPagination(page, pageSize) {
      this.pagination.page = page
      this.pagination.pageSize = pageSize
    },

    // 重置课程状态
    resetCourseState() {
      this.courses = []
      this.currentCourse = null
      this.categories = []
      this.learningRecords = {}
      this.learningProgress = {}
      this.pagination = {
        page: 1,
        pageSize: 12,
        total: 0
      }
    }
  }
})
