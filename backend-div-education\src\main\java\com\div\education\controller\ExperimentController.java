package com.div.education.controller;

import com.div.education.dto.ApiResponse;
import com.div.education.dto.PageResponse;
import com.div.education.entity.Experiment;
import com.div.education.repository.ExperimentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 实验控制器
 */
@RestController
@RequestMapping("/experiments")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ExperimentController {

    private final ExperimentRepository experimentRepository;

    /**
     * 获取实验列表
     */
    @GetMapping
    public ApiResponse<PageResponse<Experiment>> getExperiments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) Experiment.ExperimentDifficulty difficulty) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Experiment> experimentPage;
        
        if (subject != null && difficulty != null) {
            experimentPage = experimentRepository.findBySubjectAndDifficulty(subject, difficulty, pageable);
        } else if (subject != null) {
            experimentPage = experimentRepository.findBySubject(subject, pageable);
        } else if (difficulty != null) {
            experimentPage = experimentRepository.findByDifficulty(difficulty, pageable);
        } else {
            experimentPage = experimentRepository.findByStatus(Experiment.ExperimentStatus.ACTIVE, pageable);
        }
        
        return ApiResponse.success(PageResponse.of(experimentPage));
    }

    /**
     * 根据ID获取实验
     */
    @GetMapping("/{id}")
    public ApiResponse<Experiment> getExperimentById(@PathVariable Long id) {
        return experimentRepository.findById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.notFound("实验不存在"));
    }

    /**
     * 搜索实验
     */
    @GetMapping("/search")
    public ApiResponse<PageResponse<Experiment>> searchExperiments(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Experiment> experimentPage = experimentRepository.searchExperiments(keyword, pageable);
        return ApiResponse.success(PageResponse.of(experimentPage));
    }

    /**
     * 获取热门实验
     */
    @GetMapping("/popular")
    public ApiResponse<List<Experiment>> getPopularExperiments(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Experiment> experiments = experimentRepository.findPopularExperiments(pageable);
        return ApiResponse.success(experiments);
    }

    /**
     * 获取最新实验
     */
    @GetMapping("/latest")
    public ApiResponse<List<Experiment>> getLatestExperiments(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Experiment> experiments = experimentRepository.findLatestExperiments(pageable);
        return ApiResponse.success(experiments);
    }

    /**
     * 根据学科统计实验数量
     */
    @GetMapping("/stats/by-subject")
    public ApiResponse<List<Object[]>> getExperimentStatsBySubject() {
        List<Object[]> stats = experimentRepository.countExperimentsBySubject();
        return ApiResponse.success(stats);
    }

    /**
     * 创建实验
     */
    @PostMapping
    public ApiResponse<Experiment> createExperiment(@RequestBody Experiment experiment) {
        try {
            experiment.setStatus(Experiment.ExperimentStatus.ACTIVE);
            Experiment savedExperiment = experimentRepository.save(experiment);
            return ApiResponse.success("实验创建成功", savedExperiment);
        } catch (Exception e) {
            return ApiResponse.error("实验创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新实验
     */
    @PutMapping("/{id}")
    public ApiResponse<Experiment> updateExperiment(@PathVariable Long id, @RequestBody Experiment experiment) {
        try {
            if (!experimentRepository.existsById(id)) {
                return ApiResponse.notFound("实验不存在");
            }
            experiment.setId(id);
            Experiment updatedExperiment = experimentRepository.save(experiment);
            return ApiResponse.success("实验更新成功", updatedExperiment);
        } catch (Exception e) {
            return ApiResponse.error("实验更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除实验
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteExperiment(@PathVariable Long id) {
        try {
            if (!experimentRepository.existsById(id)) {
                return ApiResponse.notFound("实验不存在");
            }
            experimentRepository.deleteById(id);
            return ApiResponse.success("实验删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error("实验删除失败: " + e.getMessage());
        }
    }


}
