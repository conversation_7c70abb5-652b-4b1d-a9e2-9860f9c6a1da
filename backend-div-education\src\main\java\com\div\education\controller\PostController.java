package com.div.education.controller;

import com.div.education.dto.ApiResponse;
import com.div.education.dto.PageResponse;
import com.div.education.entity.Post;
import com.div.education.repository.PostRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 帖子控制器
 */
@RestController
@RequestMapping("/posts")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class PostController {

    private final PostRepository postRepository;

    /**
     * 获取帖子列表
     */
    @GetMapping
    public ApiResponse<PageResponse<Post>> getPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String category) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Post> postPage;
        
        if (category != null) {
            postPage = postRepository.findByCategory(category, pageable);
        } else {
            postPage = postRepository.findByStatus(Post.PostStatus.PUBLISHED, pageable);
        }
        
        return ApiResponse.success(PageResponse.of(postPage));
    }

    /**
     * 根据ID获取帖子
     */
    @GetMapping("/{id}")
    public ApiResponse<Post> getPostById(@PathVariable Long id) {
        return postRepository.findById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.notFound("帖子不存在"));
    }

    /**
     * 搜索帖子
     */
    @GetMapping("/search")
    public ApiResponse<PageResponse<Post>> searchPosts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Post> postPage = postRepository.searchPosts(keyword, pageable);
        return ApiResponse.success(PageResponse.of(postPage));
    }

    /**
     * 获取热门帖子
     */
    @GetMapping("/popular")
    public ApiResponse<List<Post>> getPopularPosts(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Post> posts = postRepository.findPopularPosts(pageable);
        return ApiResponse.success(posts);
    }

    /**
     * 获取最新帖子
     */
    @GetMapping("/latest")
    public ApiResponse<List<Post>> getLatestPosts(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Post> posts = postRepository.findLatestPosts(pageable);
        return ApiResponse.success(posts);
    }

    /**
     * 获取置顶帖子
     */
    @GetMapping("/pinned")
    public ApiResponse<List<Post>> getPinnedPosts() {
        List<Post> posts = postRepository.findByIsPinnedTrueAndStatusOrderByCreatedAtDesc(Post.PostStatus.PUBLISHED);
        return ApiResponse.success(posts);
    }

    /**
     * 根据分类统计帖子数量
     */
    @GetMapping("/stats/by-category")
    public ApiResponse<List<Object[]>> getPostStatsByCategory() {
        List<Object[]> stats = postRepository.countPostsByCategory();
        return ApiResponse.success(stats);
    }

    /**
     * 创建帖子
     */
    @PostMapping
    public ApiResponse<Post> createPost(@RequestBody Post post) {
        try {
            post.setStatus(Post.PostStatus.PUBLISHED);
            post.setViewCount(0);
            post.setLikeCount(0);
            post.setCommentCount(0);
            post.setIsPinned(false);
            Post savedPost = postRepository.save(post);
            return ApiResponse.success("帖子创建成功", savedPost);
        } catch (Exception e) {
            return ApiResponse.error("帖子创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新帖子
     */
    @PutMapping("/{id}")
    public ApiResponse<Post> updatePost(@PathVariable Long id, @RequestBody Post post) {
        try {
            if (!postRepository.existsById(id)) {
                return ApiResponse.notFound("帖子不存在");
            }
            post.setId(id);
            Post updatedPost = postRepository.save(post);
            return ApiResponse.success("帖子更新成功", updatedPost);
        } catch (Exception e) {
            return ApiResponse.error("帖子更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除帖子
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deletePost(@PathVariable Long id) {
        try {
            if (!postRepository.existsById(id)) {
                return ApiResponse.notFound("帖子不存在");
            }
            postRepository.deleteById(id);
            return ApiResponse.success("帖子删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error("帖子删除失败: " + e.getMessage());
        }
    }

    /**
     * 点赞帖子
     */
    @PostMapping("/{id}/like")
    public ApiResponse<Post> likePost(@PathVariable Long id) {
        try {
            Post post = postRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("帖子不存在"));

            // 简单的点赞逻辑，实际项目中应该记录用户点赞状态
            post.setLikeCount(post.getLikeCount() + 1);
            Post updatedPost = postRepository.save(post);

            return ApiResponse.success("点赞成功", updatedPost);
        } catch (Exception e) {
            return ApiResponse.error("点赞失败: " + e.getMessage());
        }
    }

    /**
     * 获取帖子评论
     */
    @GetMapping("/{id}/comments")
    public ApiResponse<List<Object>> getPostComments(@PathVariable Long id) {
        try {
            // 简单返回空列表，实际项目中需要实现评论实体和repository
            return ApiResponse.success("获取评论成功", new ArrayList<>());
        } catch (Exception e) {
            return ApiResponse.error("获取评论失败: " + e.getMessage());
        }
    }

    /**
     * 创建评论
     */
    @PostMapping("/{id}/comments")
    public ApiResponse<Object> createComment(@PathVariable Long id, @RequestBody Object commentData) {
        try {
            // 简单返回成功，实际项目中需要实现评论功能
            return ApiResponse.success("评论发表成功", commentData);
        } catch (Exception e) {
            return ApiResponse.error("评论发表失败: " + e.getMessage());
        }
    }


}
