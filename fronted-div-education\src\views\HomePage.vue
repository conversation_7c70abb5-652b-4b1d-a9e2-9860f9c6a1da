<template>
  <div class="home-page">
    <!-- 轮播图区域 -->
    <section class="hero-section">
      <el-carousel height="400px" indicator-position="outside">
        <el-carousel-item v-for="item in banners" :key="item.id">
          <div class="carousel-item" :style="{ backgroundImage: `url(${item.image})` }">
            <div class="carousel-content">
              <h2>{{ item.title }}</h2>
              <p>{{ item.description }}</p>
              <el-button type="primary" size="large" @click="handleBannerClick(item)">
                {{ item.buttonText }}
              </el-button>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </section>

    <!-- 课程分类导航 -->
    <section class="categories-section">
      <div class="container">
        <h2 class="section-title">课程分类</h2>
        <div class="categories-grid">
          <div 
            v-for="category in courseStore.categories" 
            :key="category.id"
            class="category-card"
            @click="goToCategory(category.id)"
          >
            <el-icon class="category-icon" size="40">
              <component :is="getCategoryIcon(category.name)" />
            </el-icon>
            <h3>{{ category.name }}</h3>
            <p>{{ category.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门课程 -->
    <section class="popular-courses-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">热门课程</h2>
          <el-button type="primary" link @click="$router.push('/learning')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="courses-grid">
          <CourseCard 
            v-for="course in courseStore.popularCourses" 
            :key="course.id"
            :course="course"
            @click="goToCourse(course.id)"
          />
        </div>
      </div>
    </section>

    <!-- 最新简讯 -->
    <section class="news-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">最新简讯</h2>
          <el-button type="primary" link @click="$router.push('/news')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="news-grid">
          <div 
            v-for="item in latestNews" 
            :key="item.id"
            class="news-card"
            @click="goToNews(item.id)"
          >
            <div class="news-image">
              <img :src="item.coverImage" :alt="item.title" />
              <span v-if="item.isHot" class="hot-badge">热门</span>
            </div>
            <div class="news-content">
              <h3>{{ item.title }}</h3>
              <p>{{ item.summary }}</p>
              <div class="news-meta">
                <span class="category">{{ item.category }}</span>
                <span class="read-time">{{ item.readTime }}分钟阅读</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 学习统计 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-item">
            <el-icon class="stat-icon" size="48"><User /></el-icon>
            <div class="stat-content">
              <h3>10,000+</h3>
              <p>注册用户</p>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon" size="48"><VideoPlay /></el-icon>
            <div class="stat-content">
              <h3>2,000+</h3>
              <p>免费课程</p>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon" size="48"><ChatDotRound /></el-icon>
            <div class="stat-content">
              <h3>50,000+</h3>
              <p>社区讨论</p>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon" size="48"><Trophy /></el-icon>
            <div class="stat-content">
              <h3>5,000+</h3>
              <p>学习小组</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCourseStore } from '../stores/course.js'
import { newsAPI } from '../mock/api.js'
import CourseCard from '../components/Course/CourseCard.vue'

const router = useRouter()
const courseStore = useCourseStore()

// 轮播图数据
const banners = ref([
  {
    id: 1,
    title: '欢迎来到DIV教育学习平台',
    description: '免费优质课程，助力你的学习之路',
    buttonText: '开始学习',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png',
    action: 'learning'
  },
  {
    id: 2,
    title: '加入学习社区',
    description: '与志同道合的伙伴一起交流学习',
    buttonText: '立即加入',
    image: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    action: 'community'
  },
  {
    id: 3,
    title: '创建学习小组',
    description: '组建你的专属学习团队',
    buttonText: '创建小组',
    image: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    action: 'groups'
  }
])

// 最新简讯
const latestNews = ref([])

// 获取分类图标
const getCategoryIcon = (categoryName) => {
  const iconMap = {
    '前端开发': 'Monitor',
    '后端开发': 'Server',
    '移动开发': 'Iphone',
    '数据科学': 'DataAnalysis',
    '设计相关': 'Brush'
  }
  return iconMap[categoryName] || 'Document'
}

// 处理轮播图点击
const handleBannerClick = (banner) => {
  switch (banner.action) {
    case 'learning':
      router.push('/learning')
      break
    case 'community':
      router.push('/community')
      break
    case 'groups':
      router.push('/groups')
      break
  }
}

// 跳转到分类页面
const goToCategory = (categoryId) => {
  router.push({
    path: '/learning',
    query: { categoryId }
  })
}

// 跳转到课程详情
const goToCourse = (courseId) => {
  router.push(`/learning/course/${courseId}`)
}

// 跳转到简讯详情
const goToNews = (newsId) => {
  router.push(`/news/${newsId}`)
}

// 获取最新简讯
const fetchLatestNews = async () => {
  try {
    // 使用模拟数据，实际项目中应该有新闻API
    latestNews.value = [
      {
        id: 1,
        title: '平台新增AI编程助手功能',
        summary: '为了提升学习体验，平台新增了AI编程助手功能，可以帮助学生解答编程问题...',
        publishTime: '2024-03-15 10:00:00'
      },
      {
        id: 2,
        title: '春季编程大赛即将开始',
        summary: '2024年春季编程大赛报名已开启，丰厚奖品等你来拿...',
        publishTime: '2024-03-14 15:30:00'
      },
      {
        id: 3,
        title: '新增云计算课程专区',
        summary: '平台新增云计算课程专区，包含AWS、Azure、阿里云等主流云平台课程...',
        publishTime: '2024-03-13 09:20:00'
      }
    ]
  } catch (error) {
    console.error('获取简讯失败:', error)
  }
}

// 页面初始化
onMounted(async () => {
  await courseStore.fetchCategories()
  await courseStore.fetchCourses()
  await fetchLatestNews()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.home-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 40px;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

/* 轮播图样式 */
.hero-section {
  margin-bottom: 80px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.carousel-item {
  height: 500px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.carousel-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  backdrop-filter: blur(2px);
}

.carousel-content {
  text-align: center;
  color: white;
  z-index: 1;
  max-width: 600px;
  padding: 0 20px;
}

.carousel-content h2 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.carousel-content p {
  font-size: 20px;
  margin-bottom: 30px;
  opacity: 0.95;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* 分类导航样式 */
.categories-section {
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  margin-bottom: 60px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 30px;
}

.category-card {
  text-align: center;
  padding: 40px 25px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.category-card:hover::before {
  left: 100%;
}

.category-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-color: rgba(102, 126, 234, 0.3);
}

.category-icon {
  color: #667eea;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.category-card:hover .category-icon {
  color: #764ba2;
  transform: scale(1.1);
}

.category-card h3 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 12px;
  font-weight: 600;
}

.category-card p {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

/* 课程网格样式 */
.popular-courses-section {
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  margin-bottom: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

/* 简讯样式 */
.news-section {
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  margin-bottom: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.news-card {
  display: flex;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.news-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 120px;
  height: 120px;
  position: relative;
  flex-shrink: 0;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hot-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #f56c6c;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.news-content {
  padding: 15px;
  flex: 1;
}

.news-content h3 {
  font-size: 16px;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.news-content p {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

/* 统计数据样式 */
.stats-section {
  padding: 60px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
}

.stat-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-icon {
  margin-bottom: 15px;
  opacity: 0.9;
}

.stat-content h3 {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-content p {
  font-size: 16px;
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .carousel-content h2 {
    font-size: 24px;
  }
  
  .carousel-content p {
    font-size: 16px;
  }
  
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .news-grid {
    grid-template-columns: 1fr;
  }
  
  .news-card {
    flex-direction: column;
  }
  
  .news-image {
    width: 100%;
    height: 200px;
  }
}
</style>
