<template>
  <div class="group-detail-page">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>
      
      <div v-else-if="!group" class="error-container">
        <el-result
          icon="warning"
          title="小组不存在"
          sub-title="抱歉，您访问的学习小组不存在或已被删除"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/groups')">
              返回小组列表
            </el-button>
          </template>
        </el-result>
      </div>
      
      <div v-else class="group-content">
        <!-- 小组头部信息 -->
        <div class="group-header">
          <div class="group-cover">
            <img :src="group.coverImage" :alt="group.name" />
          </div>
          
          <div class="group-info">
            <h1 class="group-name">{{ group.name }}</h1>
            <p class="group-description">{{ group.description }}</p>
            
            <!-- 小组统计 -->
            <div class="group-stats">
              <div class="stat-item">
                <el-icon><User /></el-icon>
                <span>{{ group.memberCount }}/{{ group.maxMembers }} 成员</span>
              </div>
              <div class="stat-item">
                <el-icon><Calendar /></el-icon>
                <span>创建于 {{ formatDate(group.createdAt) }}</span>
              </div>
              <div class="stat-item">
                <el-icon><Clock /></el-icon>
                <span>{{ group.recentActivity }}</span>
              </div>
            </div>
            
            <!-- 创建者信息 -->
            <div class="creator-info">
              <el-avatar :src="group.creator.avatar" :size="32" />
              <div class="creator-details">
                <span class="creator-label">组长</span>
                <h4>{{ group.creator.username }}</h4>
              </div>
            </div>
            
            <!-- 小组标签 -->
            <div class="group-tags">
              <el-tag 
                v-for="tag in group.tags" 
                :key="tag" 
                size="large" 
                type="info"
              >
                {{ tag }}
              </el-tag>
            </div>
            
            <!-- 操作按钮 -->
            <div class="group-actions">
              <el-button 
                type="primary" 
                size="large"
                :disabled="group.memberCount >= group.maxMembers"
                @click="handleJoinGroup"
              >
                {{ group.memberCount >= group.maxMembers ? '已满员' : '加入小组' }}
              </el-button>
              <el-button size="large">
                <el-icon><Share /></el-icon>
                分享小组
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 小组详情标签页 -->
        <div class="group-tabs">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="小组讨论" name="discussions">
              <div class="discussions-section">
                <div class="discussion-header">
                  <h3>小组讨论</h3>
                  <el-button type="primary" @click="showCreateDiscussion = true">
                    发起讨论
                  </el-button>
                </div>
                
                <div class="discussions-list">
                  <div
                    v-for="discussion in discussions"
                    :key="discussion.id"
                    class="discussion-item"
                  >
                    <div class="discussion-info">
                      <h4>{{ discussion.title }}</h4>
                      <p>{{ discussion.content }}</p>
                      <div class="discussion-meta">
                        <span>{{ discussion.author }} · {{ discussion.time }}</span>
                        <span>{{ discussion.replies }} 回复</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-if="discussions.length === 0" class="empty-discussions">
                  <el-empty description="暂无讨论，快来发起第一个讨论吧！" />
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="成员列表" name="members">
              <div class="members-section">
                <h3>小组成员 ({{ group.memberCount }})</h3>
                
                <div class="members-grid">
                  <div
                    v-for="member in members"
                    :key="member.id"
                    class="member-item"
                  >
                    <el-avatar :src="member.avatar" :size="50" />
                    <div class="member-info">
                      <h5>{{ member.name }}</h5>
                      <span class="member-role">{{ member.role }}</span>
                      <span class="member-join-time">{{ member.joinTime }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="学习资料" name="resources">
              <div class="resources-section">
                <h3>共享资料</h3>
                
                <div class="resources-list">
                  <div
                    v-for="resource in resources"
                    :key="resource.id"
                    class="resource-item"
                  >
                    <el-icon class="resource-icon"><Document /></el-icon>
                    <div class="resource-info">
                      <h5>{{ resource.name }}</h5>
                      <span class="resource-meta">{{ resource.type }} · {{ resource.size }} · {{ resource.uploadTime }}</span>
                    </div>
                    <el-button type="primary" link>下载</el-button>
                  </div>
                </div>
                
                <div v-if="resources.length === 0" class="empty-resources">
                  <el-empty description="暂无共享资料" />
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    
    <!-- 创建讨论对话框 -->
    <el-dialog
      v-model="showCreateDiscussion"
      title="发起讨论"
      width="600px"
    >
      <el-form :model="discussionForm" label-width="80px">
        <el-form-item label="讨论标题">
          <el-input v-model="discussionForm.title" placeholder="请输入讨论标题" />
        </el-form-item>
        <el-form-item label="讨论内容">
          <el-input
            v-model="discussionForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入讨论内容"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDiscussion = false">取消</el-button>
        <el-button type="primary" @click="createDiscussion">发起讨论</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { groupAPI } from '../mock/api.js'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const loading = ref(true)
const group = ref(null)
const activeTab = ref('discussions')
const showCreateDiscussion = ref(false)

// 模拟数据
const discussions = ref([
  {
    id: 1,
    title: 'Vue 3 学习心得分享',
    content: '最近学习Vue 3有一些心得，想和大家分享一下...',
    author: '张三',
    time: '2小时前',
    replies: 5
  },
  {
    id: 2,
    title: '前端面试经验总结',
    content: '刚刚结束了几轮面试，总结一些经验给大家...',
    author: '李四',
    time: '1天前',
    replies: 12
  }
])

const members = ref([
  {
    id: 1,
    name: '张三',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    role: '组长',
    joinTime: '2024-07-15'
  },
  {
    id: 2,
    name: '李四',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    role: '成员',
    joinTime: '2024-07-20'
  }
])

const resources = ref([
  {
    id: 1,
    name: 'Vue 3 官方文档.pdf',
    type: 'PDF',
    size: '2.5MB',
    uploadTime: '2024-08-15'
  },
  {
    id: 2,
    name: '前端开发规范.docx',
    type: 'Word',
    size: '1.2MB',
    uploadTime: '2024-08-10'
  }
])

// 讨论表单
const discussionForm = reactive({
  title: '',
  content: ''
})

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// 处理加入小组
const handleJoinGroup = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    ElMessage.success('加入小组成功')
    group.value.memberCount += 1
  } catch (error) {
    ElMessage.error('加入小组失败')
  }
}

// 创建讨论
const createDiscussion = () => {
  if (!discussionForm.title || !discussionForm.content) {
    ElMessage.warning('请填写完整信息')
    return
  }

  const newDiscussion = {
    id: discussions.value.length + 1,
    title: discussionForm.title,
    content: discussionForm.content,
    author: userStore.username,
    time: '刚刚',
    replies: 0
  }

  discussions.value.unshift(newDiscussion)
  
  // 重置表单
  discussionForm.title = ''
  discussionForm.content = ''
  showCreateDiscussion.value = false
  
  ElMessage.success('讨论发起成功')
}

// 获取小组详情
const fetchGroupDetail = async () => {
  const groupId = parseInt(route.params.id)
  
  try {
    loading.value = true
    const response = await groupAPI.getGroupDetail(groupId)
    group.value = response.data
  } catch (error) {
    console.error('获取小组详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await fetchGroupDetail()
})
</script>

<style scoped>
.group-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.loading-container,
.error-container {
  padding: 40px 0;
}

.group-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 30px;
}

.group-cover {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.group-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.group-info {
  flex: 1;
}

.group-name {
  font-size: 28px;
  color: #303133;
  margin-bottom: 15px;
}

.group-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 20px;
}

.group-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 14px;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.creator-label {
  font-size: 12px;
  color: #909399;
}

.creator-details h4 {
  margin: 0;
  color: #303133;
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 25px;
}

.group-actions {
  display: flex;
  gap: 15px;
}

.group-tabs {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.discussions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.discussion-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.discussion-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.discussion-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.discussion-info p {
  color: #606266;
  margin: 0 0 10px 0;
  line-height: 1.5;
}

.discussion-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.member-info h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.member-role {
  font-size: 12px;
  color: #409eff;
  display: block;
  margin-bottom: 3px;
}

.member-join-time {
  font-size: 12px;
  color: #909399;
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.resource-icon {
  font-size: 24px;
  color: #409eff;
}

.resource-info {
  flex: 1;
}

.resource-info h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.resource-meta {
  font-size: 12px;
  color: #909399;
}

.empty-discussions,
.empty-resources {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .group-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .group-cover {
    width: 100%;
    height: 150px;
  }
  
  .group-stats {
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .group-actions {
    flex-direction: column;
  }
  
  .discussion-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .members-grid {
    grid-template-columns: 1fr;
  }
}
</style>
