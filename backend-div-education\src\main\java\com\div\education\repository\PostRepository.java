package com.div.education.repository;

import com.div.education.entity.Post;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 帖子数据访问层
 */
@Repository
public interface PostRepository extends JpaRepository<Post, Long> {

    /**
     * 根据状态查找帖子
     */
    Page<Post> findByStatus(Post.PostStatus status, Pageable pageable);

    /**
     * 根据分类查找帖子
     */
    Page<Post> findByCategory(String category, Pageable pageable);

    /**
     * 根据作者查找帖子
     */
    Page<Post> findByAuthorId(Long authorId, Pageable pageable);

    /**
     * 查找置顶帖子
     */
    List<Post> findByIsPinnedTrueAndStatusOrderByCreatedAtDesc(Post.PostStatus status);

    /**
     * 根据关键词搜索帖子
     */
    @Query("SELECT p FROM Post p WHERE p.title LIKE %:keyword% OR p.content LIKE %:keyword%")
    Page<Post> searchPosts(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取热门帖子（按点赞数排序）
     */
    @Query("SELECT p FROM Post p WHERE p.status = 'PUBLISHED' ORDER BY p.likeCount DESC")
    List<Post> findPopularPosts(Pageable pageable);

    /**
     * 获取最新帖子
     */
    @Query("SELECT p FROM Post p WHERE p.status = 'PUBLISHED' ORDER BY p.createdAt DESC")
    List<Post> findLatestPosts(Pageable pageable);

    /**
     * 根据分类统计帖子数量
     */
    @Query("SELECT p.category, COUNT(p) FROM Post p WHERE p.status = 'PUBLISHED' GROUP BY p.category")
    List<Object[]> countPostsByCategory();
}
