package com.div.education.dto;

import com.div.education.entity.User;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户DTO
 */
@Data
public class UserDTO {
    private Long id;
    private String username;
    private String email;
    private String nickname;
    private String avatar;
    private String phone;
    private User.UserRole role;
    private User.UserStatus status;
    private String bio;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static UserDTO fromEntity(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setNickname(user.getNickname());
        dto.setAvatar(user.getAvatar());
        dto.setPhone(user.getPhone());
        dto.setRole(user.getRole());
        dto.setStatus(user.getStatus());
        dto.setBio(user.getBio());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
}

/**
 * 用户登录请求DTO
 */
@Data
class LoginRequest {
    private String email;
    private String password;
}

/**
 * 用户注册请求DTO
 */
@Data
class RegisterRequest {
    private String username;
    private String email;
    private String password;
    private String nickname;
    private User.UserRole role = User.UserRole.STUDENT;
}

/**
 * 用户更新请求DTO
 */
@Data
class UpdateUserRequest {
    private String nickname;
    private String avatar;
    private String phone;
    private String bio;
}

/**
 * 登录响应DTO
 */
@Data
class LoginResponse {
    private String token;
    private UserDTO user;

    public LoginResponse(String token, UserDTO user) {
        this.token = token;
        this.user = user;
    }
}
