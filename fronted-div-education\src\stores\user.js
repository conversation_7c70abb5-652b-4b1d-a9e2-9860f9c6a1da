import { defineStore } from 'pinia'
import { api } from '../api/index.js'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户信息
    user: null,
    // 登录状态
    isLoggedIn: false,
    // JWT token
    token: localStorage.getItem('token') || '',
    // 用户权限
    permissions: []
  }),

  getters: {
    // 获取用户头像
    userAvatar: (state) => {
      return state.user?.avatar || 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    
    // 获取用户名
    username: (state) => {
      return state.user?.username || '游客'
    },
    
    // 是否为老师
    isTeacher: (state) => {
      return state.user?.role === 'teacher'
    },
    
    // 是否为管理员
    isAdmin: (state) => {
      return state.user?.role === 'admin'
    }
  },

  actions: {
    // 用户登录
    async login(email, password) {
      try {
        const response = await api.auth.login(email, password)
        const { user, token } = response.data
        
        this.user = user
        this.token = token
        this.isLoggedIn = true
        
        // 保存token到localStorage
        localStorage.setItem('token', token)
        localStorage.setItem('user', JSON.stringify(user))
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 用户注册
    async register(userData) {
      try {
        const response = await api.auth.register(userData.username, userData.email, userData.password, userData.nickname, userData.role)
        return response
      } catch (error) {
        throw error
      }
    },

    // 获取用户信息
    async fetchUserProfile() {
      if (!this.user?.id) return
      
      try {
        const response = await userAPI.getProfile(this.user.id)
        this.user = response.data
        localStorage.setItem('user', JSON.stringify(this.user))
        return response
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    // 更新用户信息
    async updateProfile(updateData) {
      if (!this.user?.id) return
      
      try {
        const response = await userAPI.updateProfile(this.user.id, updateData)
        this.user = response.data
        localStorage.setItem('user', JSON.stringify(this.user))
        return response
      } catch (error) {
        throw error
      }
    },

    // 用户登出
    logout() {
      this.user = null
      this.token = ''
      this.isLoggedIn = false
      this.permissions = []
      
      // 清除localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    },

    // 初始化用户状态（从localStorage恢复）
    initializeAuth() {
      const token = localStorage.getItem('token')
      const userStr = localStorage.getItem('user')
      
      if (token && userStr) {
        try {
          this.token = token
          this.user = JSON.parse(userStr)
          this.isLoggedIn = true
        } catch (error) {
          console.error('恢复用户状态失败:', error)
          this.logout()
        }
      }
    }
  }
})
