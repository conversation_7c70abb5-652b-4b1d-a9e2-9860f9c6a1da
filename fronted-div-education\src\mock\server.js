// Mock服务器 - DIV教育学习平台
// 用于独立启动mock数据服务

import express from 'express'
import cors from 'cors'
import { 
  userAPI, 
  courseAPI, 
  communityAPI, 
  groupAPI, 
  newsAPI, 
  learningAPI, 
  experimentAPI, 
  analyticsAPI 
} from './api.js'

const app = express()
const PORT = 3001

// 中间件
app.use(cors())
app.use(express.json())

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`🎭 Mock API: ${req.method} ${req.path}`)
  next()
})

// 用户相关路由
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body
    const result = await userAPI.login(email, password)
    res.json(result)
  } catch (error) {
    res.status(400).json({ code: 400, message: error.message })
  }
})

app.post('/api/auth/register', async (req, res) => {
  try {
    const result = await userAPI.register(req.body)
    res.json(result)
  } catch (error) {
    res.status(400).json({ code: 400, message: error.message })
  }
})

app.get('/api/users/:id', async (req, res) => {
  try {
    const result = await userAPI.getProfile(parseInt(req.params.id))
    res.json(result)
  } catch (error) {
    res.status(404).json({ code: 404, message: error.message })
  }
})

// 课程相关路由
app.get('/api/courses', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, categoryId, keyword } = req.query
    const result = await courseAPI.getCourses({ 
      page: parseInt(page), 
      pageSize: parseInt(pageSize), 
      categoryId: categoryId ? parseInt(categoryId) : undefined,
      keyword 
    })
    res.json(result)
  } catch (error) {
    res.status(500).json({ code: 500, message: error.message })
  }
})

app.get('/api/courses/:id', async (req, res) => {
  try {
    const result = await courseAPI.getCourseDetail(parseInt(req.params.id))
    res.json(result)
  } catch (error) {
    res.status(404).json({ code: 404, message: error.message })
  }
})

app.get('/api/categories', async (req, res) => {
  try {
    const result = await courseAPI.getCategories()
    res.json(result)
  } catch (error) {
    res.status(500).json({ code: 500, message: error.message })
  }
})

// 实验相关路由
app.get('/api/experiments', async (req, res) => {
  try {
    const { subject, difficulty, keyword } = req.query
    const result = await experimentAPI.getExperiments({ subject, difficulty, keyword })
    res.json(result)
  } catch (error) {
    res.status(500).json({ code: 500, message: error.message })
  }
})

app.get('/api/experiments/:id', async (req, res) => {
  try {
    const result = await experimentAPI.getExperimentDetail(parseInt(req.params.id))
    res.json(result)
  } catch (error) {
    res.status(404).json({ code: 404, message: error.message })
  }
})

// 社区相关路由
app.get('/api/posts', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, keyword } = req.query
    const result = await communityAPI.getPosts({ 
      page: parseInt(page), 
      pageSize: parseInt(pageSize), 
      keyword 
    })
    res.json(result)
  } catch (error) {
    res.status(500).json({ code: 500, message: error.message })
  }
})

app.get('/api/posts/:id', async (req, res) => {
  try {
    const result = await communityAPI.getPostDetail(parseInt(req.params.id))
    res.json(result)
  } catch (error) {
    res.status(404).json({ code: 404, message: error.message })
  }
})

// 简讯相关路由
app.get('/api/news', async (req, res) => {
  try {
    const { category } = req.query
    const result = await newsAPI.getNews({ category })
    res.json(result)
  } catch (error) {
    res.status(500).json({ code: 500, message: error.message })
  }
})

// 学习小组相关路由
app.get('/api/groups', async (req, res) => {
  try {
    const { keyword } = req.query
    const result = await groupAPI.getGroups({ keyword })
    res.json(result)
  } catch (error) {
    res.status(500).json({ code: 500, message: error.message })
  }
})

// 数据分析相关路由
app.get('/api/analytics/:userId', async (req, res) => {
  try {
    const result = await analyticsAPI.getLearningAnalytics(parseInt(req.params.userId))
    res.json(result)
  } catch (error) {
    res.status(500).json({ code: 500, message: error.message })
  }
})

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    code: 200, 
    message: 'Mock服务器运行正常', 
    data: { 
      status: 'healthy',
      mode: 'mock',
      timestamp: new Date().toISOString()
    }
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`🎭 Mock服务器已启动: http://localhost:${PORT}`)
  console.log(`📋 健康检查: http://localhost:${PORT}/api/health`)
  console.log(`🔧 使用Mock数据模式`)
})

export default app
