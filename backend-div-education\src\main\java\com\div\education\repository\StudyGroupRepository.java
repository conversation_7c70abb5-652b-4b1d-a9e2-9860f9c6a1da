package com.div.education.repository;

import com.div.education.entity.StudyGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 学习小组数据访问层
 */
@Repository
public interface StudyGroupRepository extends JpaRepository<StudyGroup, Long> {

    /**
     * 根据状态查找学习小组
     */
    Page<StudyGroup> findByStatus(StudyGroup.StudyGroupStatus status, Pageable pageable);

    /**
     * 根据分类查找学习小组
     */
    Page<StudyGroup> findByCategory(String category, Pageable pageable);

    /**
     * 根据创建者查找学习小组
     */
    Page<StudyGroup> findByCreatorId(Long creatorId, Pageable pageable);

    /**
     * 查找公开的学习小组
     */
    Page<StudyGroup> findByIsPublicTrueAndStatus(StudyGroup.StudyGroupStatus status, Pageable pageable);

    /**
     * 根据关键词搜索学习小组
     */
    @Query("SELECT sg FROM StudyGroup sg WHERE sg.name LIKE %:keyword% OR sg.description LIKE %:keyword%")
    Page<StudyGroup> searchStudyGroups(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取热门学习小组（按成员数排序）
     */
    @Query("SELECT sg FROM StudyGroup sg WHERE sg.status = 'ACTIVE' AND sg.isPublic = true ORDER BY sg.currentMembers DESC")
    List<StudyGroup> findPopularStudyGroups(Pageable pageable);

    /**
     * 获取最新学习小组
     */
    @Query("SELECT sg FROM StudyGroup sg WHERE sg.status = 'ACTIVE' AND sg.isPublic = true ORDER BY sg.createdAt DESC")
    List<StudyGroup> findLatestStudyGroups(Pageable pageable);

    /**
     * 根据分类统计学习小组数量
     */
    @Query("SELECT sg.category, COUNT(sg) FROM StudyGroup sg WHERE sg.status = 'ACTIVE' GROUP BY sg.category")
    List<Object[]> countStudyGroupsByCategory();

    /**
     * 获取推荐学习小组（成员数适中且活跃）
     */
    @Query("SELECT sg FROM StudyGroup sg WHERE sg.status = 'ACTIVE' AND sg.isPublic = true AND sg.currentMembers < sg.maxMembers ORDER BY sg.currentMembers DESC")
    List<StudyGroup> findRecommendedStudyGroups(Pageable pageable);
}
