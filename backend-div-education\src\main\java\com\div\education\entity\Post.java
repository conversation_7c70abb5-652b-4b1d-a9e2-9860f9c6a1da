package com.div.education.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 社区帖子实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "posts")
public class Post extends BaseEntity {

    @Column(name = "title", nullable = false, length = 200)
    private String title;

    @Column(name = "content", columnDefinition = "TEXT", nullable = false)
    private String content;

    @Column(name = "category", nullable = false, length = 50)
    private String category;

    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags; // 标签

    @Column(name = "view_count", nullable = false)
    private Integer viewCount = 0;

    @Column(name = "like_count", nullable = false)
    private Integer likeCount = 0;

    @Column(name = "comment_count", nullable = false)
    private Integer commentCount = 0;

    @Column(name = "is_pinned", nullable = false)
    private Boolean isPinned = false;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PostStatus status = PostStatus.PUBLISHED;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "author_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "password", "email", "phone"})
    private User author;

    /**
     * 帖子状态枚举
     */
    public enum PostStatus {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        HIDDEN("已隐藏"),
        DELETED("已删除");

        private final String description;

        PostStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
