package com.div.education.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;

/**
 * 数据库信息服务 - 启动时显示数据库配置信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Order(1) // 确保在DataInitializer之前执行
public class DatabaseInfoService implements CommandLineRunner {

    private final DataSource dataSource;
    
    @Value("${database.type:h2}")
    private String databaseType;

    @Override
    public void run(String... args) throws Exception {
        printDatabaseInfo();
    }

    private void printDatabaseInfo() {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            log.info("========================================");
            log.info("DIV教育学习平台 - 数据库配置信息");
            log.info("========================================");
            log.info("配置的数据库类型: {}", databaseType.toUpperCase());
            log.info("实际数据库产品: {}", metaData.getDatabaseProductName());
            log.info("数据库版本: {}", metaData.getDatabaseProductVersion());
            log.info("JDBC URL: {}", metaData.getURL());
            log.info("JDBC驱动: {}", metaData.getDriverName());
            log.info("驱动版本: {}", metaData.getDriverVersion());
            
            if ("h2".equalsIgnoreCase(databaseType)) {
                log.info("========================================");
                log.info("H2数据库控制台访问信息:");
                log.info("URL: http://localhost:8081/api/h2-console");
                log.info("JDBC URL: jdbc:h2:mem:testdb");
                log.info("用户名: sa");
                log.info("密码: (空)");
                log.info("========================================");
            } else if ("mysql".equalsIgnoreCase(databaseType)) {
                log.info("========================================");
                log.info("MySQL数据库连接信息:");
                log.info("数据库: div_education");
                log.info("注意: 请确保MySQL服务已启动且数据库已创建");
                log.info("========================================");
            }
            
        } catch (Exception e) {
            log.error("获取数据库信息失败: {}", e.getMessage());
        }
    }
}
