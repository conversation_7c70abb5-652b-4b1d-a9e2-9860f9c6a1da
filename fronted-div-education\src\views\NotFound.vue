<template>
  <div class="not-found-page">
    <div class="not-found-content">
      <div class="error-icon">
        <el-icon size="120">
          <Warning />
        </el-icon>
      </div>
      <h1>404</h1>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在或已被删除</p>
      <div class="actions">
        <el-button type="primary" @click="$router.push('/')">
          返回首页
        </el-button>
        <el-button @click="$router.back()">
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
// 404页面逻辑
</script>

<style scoped>
.not-found-page {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
}

.not-found-content {
  text-align: center;
  padding: 40px;
}

.error-icon {
  color: #e6a23c;
  margin-bottom: 20px;
}

h1 {
  font-size: 72px;
  color: #303133;
  margin: 0 0 10px 0;
  font-weight: bold;
}

h2 {
  font-size: 24px;
  color: #606266;
  margin: 0 0 15px 0;
}

p {
  color: #909399;
  font-size: 16px;
  margin-bottom: 30px;
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}
</style>
