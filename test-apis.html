<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { margin: 5px; padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>DIV教育平台 API测试</h1>
    
    <div class="test-section">
        <h3>课程相关API测试</h3>
        <button class="test-button" onclick="testAPI('/api/courses', 'GET')">获取课程列表</button>
        <button class="test-button" onclick="testAPI('/api/courses/categories', 'GET')">获取课程分类</button>
        <button class="test-button" onclick="testAPI('/api/courses/popular', 'GET')">获取热门课程</button>
        <button class="test-button" onclick="testAPI('/api/courses/latest', 'GET')">获取最新课程</button>
        <button class="test-button" onclick="testAPI('/api/courses/search?keyword=Vue', 'GET')">搜索课程</button>
        <div id="courses-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>帖子相关API测试</h3>
        <button class="test-button" onclick="testAPI('/api/posts', 'GET')">获取帖子列表</button>
        <button class="test-button" onclick="testAPI('/api/posts/popular', 'GET')">获取热门帖子</button>
        <button class="test-button" onclick="testAPI('/api/posts/latest', 'GET')">获取最新帖子</button>
        <button class="test-button" onclick="testAPI('/api/posts/search?keyword=Vue', 'GET')">搜索帖子</button>
        <div id="posts-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>实验相关API测试</h3>
        <button class="test-button" onclick="testAPI('/api/experiments', 'GET')">获取实验列表</button>
        <button class="test-button" onclick="testAPI('/api/experiments/popular', 'GET')">获取热门实验</button>
        <button class="test-button" onclick="testAPI('/api/experiments/latest', 'GET')">获取最新实验</button>
        <button class="test-button" onclick="testAPI('/api/experiments/search?keyword=化学', 'GET')">搜索实验</button>
        <div id="experiments-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>用户相关API测试</h3>
        <button class="test-button" onclick="testAPI('/api/users', 'GET')">获取用户列表</button>
        <button class="test-button" onclick="testAPI('/api/users/search?keyword=学生', 'GET')">搜索用户</button>
        <div id="users-result" class="result"></div>
    </div>

    <script>
        async function testAPI(url, method = 'GET', body = null) {
            const baseUrl = 'http://localhost:8081';
            const fullUrl = baseUrl + url;
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                console.log(`测试API: ${method} ${fullUrl}`);
                const response = await fetch(fullUrl, options);
                const data = await response.json();
                
                const resultDiv = getResultDiv(url);
                if (response.ok && data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${method} ${url}\n状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ${method} ${url}\n状态: ${response.status}\n错误: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                const resultDiv = getResultDiv(url);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ${method} ${url}\n网络错误: ${error.message}`;
            }
        }
        
        function getResultDiv(url) {
            if (url.includes('/courses')) return document.getElementById('courses-result');
            if (url.includes('/posts')) return document.getElementById('posts-result');
            if (url.includes('/experiments')) return document.getElementById('experiments-result');
            if (url.includes('/users')) return document.getElementById('users-result');
            return document.createElement('div');
        }
        
        // 页面加载时自动测试基础API
        window.onload = function() {
            console.log('开始API测试...');
            setTimeout(() => testAPI('/api/courses', 'GET'), 1000);
            setTimeout(() => testAPI('/api/posts', 'GET'), 2000);
            setTimeout(() => testAPI('/api/experiments', 'GET'), 3000);
        };
    </script>
</body>
</html>
