<template>
  <el-footer class="app-footer">
    <div class="footer-content">
      <div class="footer-section">
        <h4>关于我们</h4>
        <ul>
          <li><a href="#">平台介绍</a></li>
          <li><a href="#">团队介绍</a></li>
          <li><a href="#">联系我们</a></li>
          <li><a href="#">加入我们</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h4>学习资源</h4>
        <ul>
          <li><a href="#">前端开发</a></li>
          <li><a href="#">后端开发</a></li>
          <li><a href="#">移动开发</a></li>
          <li><a href="#">数据科学</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h4>社区服务</h4>
        <ul>
          <li><a href="#">学习小组</a></li>
          <li><a href="#">技术问答</a></li>
          <li><a href="#">经验分享</a></li>
          <li><a href="#">项目展示</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h4>帮助支持</h4>
        <ul>
          <li><a href="#">使用指南</a></li>
          <li><a href="#">常见问题</a></li>
          <li><a href="#">意见反馈</a></li>
          <li><a href="#">服务条款</a></li>
        </ul>
      </div>
    </div>
    
    <div class="footer-bottom">
      <div class="copyright">
        <p>&copy; 2024 DIV教育学习平台. 保留所有权利.</p>
        <p>本项目仅用于学习演示，不用于商业用途</p>
      </div>
      <div class="social-links">
        <el-icon class="social-icon"><ChatDotRound /></el-icon>
        <el-icon class="social-icon"><Message /></el-icon>
        <el-icon class="social-icon"><Share /></el-icon>
      </div>
    </div>
  </el-footer>
</template>

<script setup>
// 这里可以添加footer相关的逻辑
</script>

<style scoped>
.app-footer {
  height: 400px;
  background: #2c3e50;
  color: #ecf0f1;
  padding: 40px 0 20px;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  padding: 0 20px;
}

.footer-section h4 {
  color: #3498db;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: #3498db;
}

.footer-bottom {
  max-width: 1200px;
  margin: 30px auto 0;
  padding: 20px;
  border-top: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.copyright p {
  margin: 0;
  font-size: 12px;
  color: #95a5a6;
  line-height: 1.5;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-icon {
  font-size: 20px;
  color: #bdc3c7;
  cursor: pointer;
  transition: color 0.3s;
}

.social-icon:hover {
  color: #3498db;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
